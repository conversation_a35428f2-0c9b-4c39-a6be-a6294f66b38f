# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production
.env.staging

# Sensitive configuration files
config/secrets.json
config/credentials.json
*.key
*.pem
*.p12
*.pfx

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.backup
*.bak
*.tmp

# IDE and editor specific files
.vscode/settings.json
.vscode/launch.json
.idea/workspace.xml
.idea/tasks.xml
.idea/dictionaries/
.idea/shelf/

# OS specific files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
.Trash-*

# Security and credential files
.aws/
.ssh/
.gnupg/
credentials
secrets
*.secret

# Testing
/coverage

# Production build
/build
# Resource Explorer Integration

This document describes the integration of AWS Resource Explorer for automatic region discovery in CloudAudit scans.

## Overview

The Resource Explorer integration eliminates the need to manually specify regions when creating scans. Instead, the system automatically discovers which AWS regions contain resources for the enabled services using AWS Resource Explorer.

## Key Features

- **Automatic Region Discovery**: Uses AWS Resource Explorer to find regions containing resources for enabled services
- **Fallback Support**: Falls back to manually specified regions if discovery fails
- **Service-Specific Mapping**: Maps each service to its specific regions
- **Optional Region Selection**: Users can still manually specify regions if desired

## Architecture

### Components

1. **ResourceExplorerService** (`app/core/services/resource_explorer.py`)
   - Core service for AWS Resource Explorer integration
   - Handles region discovery for enabled services
   - Provides both detailed and simplified region mapping

2. **Updated Scan Service** (`app/core/services/scans.py`)
   - Modified `CreateScanService` to use Resource Explorer
   - Added `discover_regions_for_services()` method
   - Integrated region discovery into scan workflow

3. **Updated Schema** (`app/common/schema.py`)
   - Made `regions` field optional in `CreateScanSchema`
   - Updated validation to allow empty regions

4. **Updated Frontend** (`cloudaudit-frontend/src/components/ScanModal.tsx`)
   - Made region selection optional
   - Added UI hints about automatic discovery
   - Updated scan summary to reflect auto-discovery

## Service Mapping

The integration maps AWS services to their corresponding CloudFormation resource types:

```python
SERVICES_RESOURCE_TYPES = {
    'ec2': ['AWS::EC2::Instance', 'AWS::EC2::Volume', 'AWS::EC2::SecurityGroup', 'AWS::EC2::VPC'],
    'rds': ['AWS::RDS::DBInstance', 'AWS::RDS::DBCluster', 'AWS::RDS::DBSnapshot'],
    'lambda': ['AWS::Lambda::Function'],
    'ecs': ['AWS::ECS::Cluster', 'AWS::ECS::Service', 'AWS::ECS::TaskDefinition'],
    'eks': ['AWS::EKS::Cluster'],
    'elb': ['AWS::ElasticLoadBalancing::LoadBalancer', 'AWS::ElasticLoadBalancingV2::LoadBalancer'],
    'efs': ['AWS::EFS::FileSystem'],
    'elasticache': ['AWS::ElastiCache::CacheCluster', 'AWS::ElastiCache::ReplicationGroup'],
    's3': ['AWS::S3::Bucket'],
    'iam': ['AWS::IAM::Role', 'AWS::IAM::User', 'AWS::IAM::Policy']
}
```

## Workflow

### 1. Scan Initiation
1. User selects services to scan
2. User optionally selects regions (if not selected, auto-discovery is used)
3. Scan request is sent to backend

### 2. Region Discovery (Backend)
1. `CreateScanService.process()` is called
2. `discover_regions_for_services()` is called with enabled services
3. Resource Explorer queries are made for each service's resource types
4. Regions are extracted from found resources
5. Discovered regions are used for the scan

### 3. Fallback Behavior
- If Resource Explorer fails, falls back to manually specified regions
- If no regions are specified and discovery fails, scan may fail gracefully
- Logs are provided for debugging discovery issues

## API Changes

### Backend Changes

#### CreateScanSchema
```python
class CreateScanSchema(BaseModel):
    account_id: int
    cloud_provider_id: int
    cloud_provider_name: str
    services: list[int]
    regions: list[str] = []  # Now optional
```

#### New Method in CreateScanService
```python
async def discover_regions_for_services(self, services):
    """Discover regions containing resources for the enabled services."""
    # Implementation details...
```

### Frontend Changes

#### CreateScanRequest
```typescript
export interface CreateScanRequest {
    user_id: number;
    account_id: number;
    cloud_provider_id: number;
    cloud_provider_name: string;
    regions?: string[];  // Now optional
    services: number[];
}
```

#### UI Updates
- Region selection is now optional
- Added explanatory text about automatic discovery
- Updated scan summary to show "auto-discovered regions" when no regions are selected

## Configuration

### AWS Resource Explorer Setup

1. **Enable Resource Explorer** in your AWS account
2. **Create an Index** in the desired region (typically ap-south-1)
3. **Configure Aggregator** to include all regions
4. **Ensure IAM permissions** for Resource Explorer access

### Required IAM Permissions

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "resource-explorer-2:Search",
                "resource-explorer-2:GetIndex",
                "resource-explorer-2:ListIndexes"
            ],
            "Resource": "*"
        }
    ]
}
```

## Testing

### Test Script
A test script is provided at `test_resource_explorer.py`:

```bash
cd cloudaudit-backend
python test_resource_explorer.py
```

### Manual Testing
1. Create a scan without specifying regions
2. Verify that regions are discovered automatically
3. Check logs for discovery process
4. Verify scan runs in discovered regions

## Error Handling

### Common Issues

1. **Resource Explorer Not Enabled**
   - Error: "Resource Explorer not available"
   - Solution: Enable Resource Explorer in AWS account

2. **Insufficient Permissions**
   - Error: "Access denied"
   - Solution: Add required IAM permissions

3. **No Resources Found**
   - Warning: "No regions discovered"
   - Fallback: Uses manually specified regions

4. **Service Not Supported**
   - Warning: "Service not supported for region discovery"
   - Fallback: Uses manually specified regions

## Logging

The integration provides comprehensive logging:

- `🔍` Discovery start/completion
- `✅` Successful resource type queries
- `❌` Failed resource type queries
- `⚠️` Warnings and fallbacks
- `📊` Summary information

## Benefits

1. **Improved User Experience**: No need to manually specify regions
2. **Accurate Scanning**: Only scans regions that actually contain resources
3. **Reduced Overhead**: Avoids scanning empty regions
4. **Automatic Updates**: Regions are discovered fresh for each scan
5. **Backward Compatibility**: Still supports manual region specification

## Future Enhancements

1. **Caching**: Cache discovered regions for a period
2. **Service-Specific Regions**: Use service-specific region mapping in scan tasks
3. **Multi-Provider Support**: Extend to other cloud providers
4. **Real-time Updates**: Update regions during long-running scans
5. **Analytics**: Track region discovery patterns and performance

## CloudAudit Project Index

This document provides a high-level map of the repository so you can quickly navigate major components and find key files.

### Top-level layout

- `cloudaudit-backend/`: FastAPI backend, Celery tasks, services, and persistence
- `cloudaudit-frontend/`: React + TypeScript SPA (Vite, Tai<PERSON>wind)
- `cloudaudit-terraform/`: Infrastructure as Code for AWS (VPC, ALB, RDS, S3, EventBridge)

---

## Backend (`cloudaudit-backend/`)

### Stack
- FastAPI app with modular APIs under `app/apis/`
- Services live under `app/core/services/` (cloud provider integrations, scans, auth, queue)
- SQLAlchemy models in `app/core/models/`
- Celery configured in `app/core/services/celery_conf/`
- Dockerized with `Dockerfile` and `docker-compose.yml`

### Key entry points
- `app/run.py`: App runner (FastAPI / Uvicorn)
- `app/manage.py`: Management utilities (scripts/commands)
- `app/app/config.py`: App configuration

### API layer (`app/apis/`)
- `auth/`: `login.py`, `logout.py`, `refresh_token.py`, `signup.py`
- Feature APIs: `accounts.py`, `cloud_providers.py`, `findings.py`, `health_checkup.py`, `regions.py`, `roles.py`, `scans.py`, `services.py`, `users.py`

### Core services (`app/core/services/`)
- `auth/`: authentication/authorization services
- `api_cloud_providers/`: provider-specific integrations
  - `aws/` → nested service checks (e.g., EC2, EBS encryption)
- `queue_service/`: asynchronous job queue integration
- `celery_conf/`: Celery app and configuration
- `cloud_providers.py`, `findings.py`, `regions.py`, `roles.py`, `scans.py`, `services.py`, `users.py`: domain services
- `websocket_handler.py`: real-time updates via websockets

### Data layer (`app/core/models/`)
- `mysql/`: SQLAlchemy models and database configuration for MySQL

### Common utilities (`app/common/`)
- `constants.py`, `enums.py`: domain enums and constants
- `errors.py`, `exception.py`: error types and handling
- `decorators.py`, `utils.py`: cross-cutting helpers
- `logger.py`: logging setup
- `messenger.py`, `slack.py`: messaging/Slack integration
- `schema.py`: shared schemas

### Supporting files
- `requirements.txt`, `pyproject.toml`: Python dependencies and build config
- `docker-entrypoint.sh`, `Dockerfile`, `docker-compose.yml`
- `scripts/`: SQL scripts (e.g., ownership/role transfers)
- `docs/workspace_ownership_testing.md`: documentation

---

## Frontend (`cloudaudit-frontend/`)

### Stack
- React + TypeScript (Vite)
- Tailwind CSS
- ESLint config at `eslint.config.js`

### Key entry points
- `src/main.tsx`: App bootstrap
- `src/App.tsx`: Root component
- `index.html`: HTML shell

### Notable directories
- `src/components/`: UI components (auth, findings, UI primitives, modals, cards)
- `src/pages/`: Routed pages (auth and dashboard views)
- `src/layouts/`: `AuthLayout`, `DashboardLayout`
- `src/hooks/`: data and auth hooks (`useAuth`, `useScans`, etc.)
- `src/services/`: API clients (`auth.ts`, `scan.ts`, `user.ts`, etc.)
- `src/stores/`: client-side stores (e.g., `userStore.ts`, `scanStore.ts`)
- `src/types/`: API and permission types
- `src/config/environment.ts`: environment handling

### DevOps
- `Dockerfile`, `docker-compose.yml`, `docker-compose.dev.yml`, `docker-compose.staging.yml`
- `scripts/`: environment validation and setup
- `tailwind.config.js`, `postcss.config.js`

---

## Terraform (`cloudaudit-terraform/terraform/`)

### Purpose
Defines AWS infrastructure: VPC, ALB, EC2, RDS, S3, and EventBridge.

### Key files
- `versions.tf`, `backend.tf`: Terraform versioning and remote state backend
- `vars.tf`: input variables
- `vpc.tf`, `alb.tf`, `ec2.tf`: network and compute resources
- `rds.tf`: database
- `s3.tf`: storage
- `eventbridge.tf`: event bus and rules
- `prod/` and `stag/`: environment-specific backends and `*.tfvars`

---

## How things fit together

1. Frontend calls Backend APIs via `src/services/*` using environment-configured base URLs.
2. Backend FastAPI endpoints delegate to services in `app/core/services/*`.
3. Long-running or scheduled work is handled by Celery workers (`celery_conf`, `queue_service`).
4. Persistence is managed via SQLAlchemy models under `app/core/models/mysql/` (MySQL).
5. Real-time updates may be sent via websockets (`websocket_handler.py`).
6. Terraform defines the AWS infra to host the application.

---

## Navigation shortcuts

- Start server: `cloudaudit-backend/app/run.py`
- APIs: `cloudaudit-backend/app/apis/`
- Business logic: `cloudaudit-backend/app/core/services/`
- Models/DB: `cloudaudit-backend/app/core/models/mysql/`
- Frontend root: `cloudaudit-frontend/src/`
- Frontend services (API clients): `cloudaudit-frontend/src/services/`
- Terraform root: `cloudaudit-terraform/terraform/`

---

## Notes and next steps

- Add service-by-service API documentation or an OpenAPI snapshot for quick reference.
- Document environment variables for backend and frontend in their respective READMEs.
- Include run/deploy instructions if you want this INDEX to double as a quickstart.



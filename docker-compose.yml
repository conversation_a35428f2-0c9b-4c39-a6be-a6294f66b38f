# Docker Compose configuration for CloudAudit Frontend
version: '3.8'

services:
  # Frontend Application
  cloudaudit-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
      args:
        BUILD_MODE: production
        VITE_BACKEND_LOCAL_BASE_URL: ${VITE_BACKEND_LOCAL_BASE_URL:-https://api.cloudaudit.com/api}
        VITE_APP_ENV: ${VITE_APP_ENV:-production}
        VITE_APP_NAME: ${VITE_APP_NAME:-CloudAudit}
        VITE_APP_BASE_URL: ${VITE_APP_BASE_URL:-https://app.cloudaudit.com}
        VITE_SENTRY_DSN: ${VITE_SENTRY_DSN}
        VITE_GA_ID: ${VITE_GA_ID}
        VITE_CDN_BASE_URL: ${VITE_CDN_BASE_URL}
    container_name: cloudaudit-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    networks:
      - cloudaudit-network
    healthcheck:
      test: ["CMD", "/usr/local/bin/healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.cloudaudit-frontend.rule=Host(`app.cloudaudit.com`)"
      - "traefik.http.routers.cloudaudit-frontend.tls=true"
      - "traefik.http.routers.cloudaudit-frontend.tls.certresolver=letsencrypt"
      - "traefik.http.services.cloudaudit-frontend.loadbalancer.server.port=80"
    volumes:
      # Optional: Mount logs for external log aggregation
      - ./logs/nginx:/var/log/nginx:rw
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M

  # Optional: Reverse Proxy (Traefik)
  traefik:
    image: traefik:v2.10
    container_name: cloudaudit-traefik
    restart: unless-stopped
    command:
      - "--api.dashboard=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.letsencrypt.acme.tlschallenge=true"
      - "--certificatesresolvers.letsencrypt.acme.email=${ACME_EMAIL:-<EMAIL>}"
      - "--certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json"
      - "--global.checknewversion=false"
      - "--global.sendanonymoususage=false"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./letsencrypt:/letsencrypt
    networks:
      - cloudaudit-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.cloudaudit.com`)"
      - "traefik.http.routers.traefik.tls=true"
      - "traefik.http.routers.traefik.tls.certresolver=letsencrypt"
      - "traefik.http.routers.traefik.service=api@internal"
    profiles:
      - traefik

networks:
  cloudaudit-network:
    driver: bridge
    name: cloudaudit-network

volumes:
  letsencrypt:
    driver: local

#!/usr/bin/env python3
"""
Test script for Resource Explorer integration.
This script tests the Resource Explorer service with sample credentials.
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core.services.resource_explorer import ResourceExplorerService


async def test_resource_explorer():
    """Test the Resource Explorer service with sample data."""
    
    # Sample credentials (replace with actual test credentials)
    test_credentials = {
        'access_key': 'your-access-key-here',
        'secret_key': 'your-secret-key-here',
        'aws_account_id': '************'
    }
    
    # Sample enabled services
    enabled_services = [
        {'id': 1, 'name': 'ec2'},
        {'id': 2, 'name': 's3'},
        {'id': 3, 'name': 'rds'},
        {'id': 4, 'name': 'lambda'},
        {'id': 5, 'name': 'iam'}
    ]
    
    print("🧪 Testing Resource Explorer Service")
    print("=" * 50)
    
    try:
        # Initialize the service
        resource_explorer = ResourceExplorerService(test_credentials)
        
        print(f"📋 Testing with {len(enabled_services)} enabled services:")
        for service in enabled_services:
            print(f"  - {service['name']}")
        
        print("\n🔍 Discovering regions...")
        
        # Test region discovery
        discovered_regions = await resource_explorer.get_regions_for_scan(enabled_services)
        
        print(f"\n✅ Discovery completed!")
        print(f"🌍 Found {len(discovered_regions)} regions: {discovered_regions}")
        
        # Test detailed service-region mapping
        print("\n📊 Testing detailed service-region mapping...")
        service_region_mapping = await resource_explorer.get_service_region_mapping(enabled_services)
        
        print("Service-Region Mapping:")
        for service_name, regions in service_region_mapping.items():
            if regions:
                print(f"  {service_name}: {regions}")
            else:
                print(f"  {service_name}: No regions found")
        
        print("\n✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("⚠️  Note: This test requires valid AWS credentials.")
    print("   Update the test_credentials dictionary with your actual credentials.")
    print("   Make sure your AWS account has Resource Explorer enabled.")
    print()
    
    # Run the test
    asyncio.run(test_resource_explorer())

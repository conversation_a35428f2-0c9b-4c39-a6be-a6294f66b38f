# Environment Configuration Guide

This guide explains how to properly configure environment variables for the CloudAudit frontend application.

## Overview

The application supports three environments:
- **Development** (`.env.local`)
- **Staging** (`.env.staging`)
- **Production** (`.env.production`)

## Quick Setup

### 1. Development Environment

For local development, create a `.env.local` file:

```bash
cp .env.example .env.local
```

The `.env.local` file is already created and configured for local development.

### 2. Staging Environment

The `.env.staging` file is already configured for staging deployment.

### 3. Production Environment

The `.env.production` file is configured for production deployment.

## Environment Variables Reference

### Required Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `VITE_BACKEND_LOCAL_BASE_URL` | Backend API endpoint | `https://api.cloudaudit.com/api` |
| `VITE_APP_NAME` | Application name | `CloudAudit` |
| `VITE_APP_BASE_URL` | Frontend application URL | `https://app.cloudaudit.com` |
| `VITE_APP_ENV` | Environment name | `production` |

### Optional Variables

| Variable | Description | Default | Environment |
|----------|-------------|---------|-------------|
| `VITE_API_TIMEOUT` | API request timeout (ms) | `30000` | All |
| `VITE_MAX_RETRIES` | Max API retry attempts | `2` | All |
| `VITE_ENABLE_DEVTOOLS` | Enable React Query DevTools | `true` (dev), `false` (prod) | All |
| `VITE_ENABLE_DEBUG_LOGGING` | Enable debug logging | `true` (dev), `false` (prod) | All |
| `VITE_ENABLE_PERFORMANCE_MONITORING` | Enable performance monitoring | `false` (dev), `true` (prod) | All |
| `VITE_ENABLE_ERROR_REPORTING` | Enable error reporting | `false` (dev), `true` (prod) | All |
| `VITE_TOKEN_REFRESH_THRESHOLD` | Token refresh threshold (minutes) | `5` | All |
| `VITE_SESSION_TIMEOUT` | Session timeout (minutes) | `60` | All |
| `VITE_SENTRY_DSN` | Sentry error reporting DSN | - | Production |
| `VITE_GA_ID` | Google Analytics ID | - | Production |
| `VITE_CDN_BASE_URL` | CDN base URL for assets | - | Production |

### Development-Only Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `VITE_DEV_HOST` | Development server host | `0.0.0.0` |
| `VITE_DEV_PORT` | Development server port | `5173` |
| `VITE_ENABLE_HMR` | Enable Hot Module Replacement | `true` |
| `VITE_ENABLE_SOURCE_MAPS` | Enable source maps | `true` |
| `VITE_BUILD_OUTPUT_DIR` | Build output directory | `dist` |

## Environment Validation

The application includes automatic environment validation:

### Manual Validation

```bash
# Validate development environment
npm run validate:env:dev

# Validate staging environment
npm run validate:env:staging

# Validate production environment
npm run validate:env:production
```

### Automatic Validation

Environment validation runs automatically before:
- Starting development server (`npm run dev`)
- Building for any environment (`npm run build:*`)

### Validation Rules

1. **Required Variables**: Must be present and non-empty
2. **URL Validation**: URLs must be valid and properly formatted
3. **Numeric Validation**: Numeric variables must contain valid numbers
4. **Environment-Specific Rules**:
   - Production: Debug logging and DevTools should be disabled
   - Production: Should not use localhost URLs
   - Production: Should have error reporting configured

## Environment-Specific Configuration

### Development

- Debug logging enabled
- DevTools enabled
- Error reporting disabled
- Performance monitoring disabled
- Source maps enabled
- No caching for React Query

### Staging

- Debug logging enabled (for debugging)
- DevTools enabled (for debugging)
- Error reporting enabled
- Performance monitoring enabled
- Mirrors production configuration

### Production

- Debug logging disabled
- DevTools disabled
- Error reporting enabled
- Performance monitoring enabled
- Source maps disabled
- Optimized caching

## Configuration Management

### Centralized Configuration

All environment variables are managed through `src/config/environment.ts`:

```typescript
import { config } from './config/environment';

// Access configuration
console.log(config.BACKEND_BASE_URL);
console.log(config.ENABLE_DEBUG_LOGGING);
```

### Environment Detection

```typescript
import { isDevelopment, isProduction, isStaging } from './config/environment';

if (isDevelopment()) {
  // Development-specific code
}
```

### Configuration Validation

```typescript
import { validateEnvironment } from './config/environment';

const validation = validateEnvironment();
if (!validation.isValid) {
  console.error('Environment validation failed:', validation.errors);
}
```

## Troubleshooting

### Common Issues

1. **Missing .env.local file**
   ```bash
   cp .env.example .env.local
   ```

2. **Invalid URL format**
   - Ensure URLs include protocol (http:// or https://)
   - Remove trailing slashes

3. **Environment validation fails**
   - Run `npm run validate:env:dev` to see specific errors
   - Check that all required variables are set

4. **DevTools not showing in development**
   - Ensure `VITE_ENABLE_DEVTOOLS=true` in `.env.local`

5. **API requests failing**
   - Verify `VITE_BACKEND_LOCAL_BASE_URL` is correct
   - Check if backend server is running

### Debug Environment Issues

1. **Check current configuration**:
   ```javascript
   // In browser console
   console.log(window.__ENV_CONFIG__);
   ```

2. **Validate environment**:
   ```bash
   npm run validate:env
   ```

3. **Check loaded environment variables**:
   ```bash
   # Development
   npm run validate:env:dev
   ```

## Security Considerations

1. **Never commit sensitive data**:
   - `.env.local` is gitignored
   - Use placeholder values in `.env.example`

2. **Production secrets**:
   - Use environment variables in CI/CD
   - Never hardcode API keys or secrets

3. **URL validation**:
   - Always validate URLs before use
   - Use HTTPS in production

## CI/CD Integration

### Environment Variables in CI/CD

Set these variables in your CI/CD pipeline:

```bash
VITE_BACKEND_LOCAL_BASE_URL=https://api.cloudaudit.com/api
VITE_APP_BASE_URL=https://app.cloudaudit.com
VITE_SENTRY_DSN=your-sentry-dsn
VITE_GA_ID=your-ga-id
```

### Build Commands

```bash
# Development build
npm run build

# Staging build
npm run build:staging

# Production build
npm run build:production
```

Each build command automatically validates the corresponding environment before building.

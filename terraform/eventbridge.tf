resource "aws_scheduler_schedule" "cloudaudit_stag_Instance_start" {
  description                  = null
  end_date                     = null
  group_name                   = "default"
  kms_key_arn                  = null
  name                         = "cloudaudit_stag_Instance_start"
  name_prefix                  = null
  schedule_expression          = "cron(30 9 ? * 2-6 *)"
  schedule_expression_timezone = "Asia/Calcutta"
  start_date                   = null
  state                        = "ENABLED"
  flexible_time_window {
    maximum_window_in_minutes = 5
    mode                      = "FLEXIBLE"
  }
  target {
    arn = "arn:aws:scheduler:::aws-sdk:ec2:startInstances"
    input = jsonencode({
      InstanceIds = ["i-0e4e6ddab7fdd15e7"]
    })
    role_arn = var.eventbridge_ec2_role_arn
    retry_policy {
      maximum_event_age_in_seconds = 86400
      maximum_retry_attempts       = 0
    }
  }

}


resource "aws_scheduler_schedule" "cloudaudit_stag_Instance_stop" {
  description                  = null
  end_date                     = null
  group_name                   = "default"
  kms_key_arn                  = null
  name                         = "cloudaudit_stag_Instance_stop"
  name_prefix                  = null
  schedule_expression          = "cron(0 19 ? * 2-6 *)"
  schedule_expression_timezone = "Asia/Calcutta"
  start_date                   = null
  state                        = "ENABLED"
  flexible_time_window {
    maximum_window_in_minutes = 5
    mode                      = "FLEXIBLE"
  }
  target {
    arn = "arn:aws:scheduler:::aws-sdk:ec2:stopInstances"
    input = jsonencode({
      InstanceIds = ["i-0e4e6ddab7fdd15e7"]
    })
    role_arn = var.eventbridge_ec2_role_arn
    retry_policy {
      maximum_event_age_in_seconds = 86400
      maximum_retry_attempts       = 0
    }
  }

}


resource "aws_scheduler_schedule" "cloudaudit_stag_rds_start" {
  description                  = null
  end_date                     = null
  group_name                   = "default"
  kms_key_arn                  = null
  name                         = "cloudaudit_stag_rds_start"
  name_prefix                  = null
  schedule_expression          = "cron(20 9 ? * 2-6 *)"
  schedule_expression_timezone = "Asia/Calcutta"
  start_date                   = null
  state                        = "ENABLED"
  flexible_time_window {
    maximum_window_in_minutes = 5
    mode                      = "FLEXIBLE"
  }
  target {
    arn = "arn:aws:scheduler:::aws-sdk:rds:startDBInstance"
    input = jsonencode({
      DbInstanceIdentifier = "cloudaudit-staging"
    })
    role_arn = var.eventbridge_rds_role_arn
    retry_policy {
      maximum_event_age_in_seconds = 86400
      maximum_retry_attempts       = 0
    }
  }

}

resource "aws_scheduler_schedule" "cloudaudit_stag_rds_stop" {
  description                  = null
  end_date                     = null
  group_name                   = "default"
  kms_key_arn                  = null
  name                         = "cloudaudit_stag_rds_stop"
  name_prefix                  = null
  schedule_expression          = "cron(0 19 ? * 2-6 *)"
  schedule_expression_timezone = "Asia/Calcutta"
  start_date                   = null
  state                        = "ENABLED"
  flexible_time_window {
    maximum_window_in_minutes = 5
    mode                      = "FLEXIBLE"
  }
  target {
    arn = "arn:aws:scheduler:::aws-sdk:rds:stopDBInstance"
    input = jsonencode({
      DbInstanceIdentifier = "cloudaudit-staging"
    })
    role_arn = var.eventbridge_rds_role_arn
    retry_policy {
      maximum_event_age_in_seconds = 86400
      maximum_retry_attempts       = 0
    }
  }

}





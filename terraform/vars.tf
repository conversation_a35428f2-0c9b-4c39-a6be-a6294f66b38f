variable "APP_NAME" {
  type    = string
  default = "cloudaudit"
}

variable "env_suffix" {
  type        = string
  description = "Can be either -staging (staging) or empty (production)."
}

variable "env" {
  type        = string
  description = "Can be either Staging or Production"
}

variable "vpc_cidr" {
  description = "CIDR block for the VPC"
  type        = string
}

variable "public1_cidr" {
  description = "CIDR block for Public Subnet 1"
  type        = string
}

variable "private1_cidr" {
  description = "CIDR block for Private Subnet 1"
  type        = string
}

variable "public2_cidr" {
  description = "CIDR block for Public Subnet 2"
  type        = string
}

variable "private2_cidr" {
  description = "CIDR block for Private Subnet 2"
  type        = string
}

variable "acm_cert_arn" {
  description = "ARN for CloudAudit ACM certificate"
  default     = "arn:aws:acm:us-east-1:255358890781:certificate/8ab49cf3-e8f7-4ada-abbd-69786361bc38"
}

variable "eventbridge_ec2_role_arn" {
  description = "ARN for the EventBridge EC2 role"
  default     = "arn:aws:iam::255358890781:role/EC2-Role-for-eventbridge"
}

variable "eventbridge_rds_role_arn" {
  description = "ARN for the EventBridge RDS role"
  default     = "arn:aws:iam::255358890781:role/EventBridgeSchedulerRDSRole"
}
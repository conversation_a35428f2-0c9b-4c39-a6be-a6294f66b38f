
# 
# Security Group for RDS
# 

resource "aws_security_group" "cloudaudit_rds_sg" {
  name        = "${var.APP_NAME}${var.env_suffix}-rds-sg"
  description = "Security group for CloudAudit RDS"
  vpc_id      = aws_vpc.cloudaudit_vpc.id

  # Allow MySQL access only from EC2 SG
  ingress {
    description     = "Allow MySQL from EC2 SG"
    from_port       = 3306
    to_port         = 3306
    protocol        = "tcp"
    security_groups = [aws_security_group.cloudaudit_ec2_sg.id]
  }

  # Outbound - all
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "${var.APP_NAME}${var.env_suffix}-rds-sg"
    Environment = "${var.env}"
    ManagedBy   = "Terraform"
  }
}

# 
# Subnet Group (Private Subnets)
# 

resource "aws_db_subnet_group" "private_cloudaudit_sb_grp" {
  name = "private-${var.APP_NAME}${var.env_suffix}-sb-grp"
  subnet_ids = [
    aws_subnet.private1.id,
    aws_subnet.private2.id
  ]

  tags = {
    Name        = "private-${var.APP_NAME}${var.env_suffix}-sb-grp"
    Environment = "${var.env}"
    ManagedBy   = "Terraform"
  }
}

# 
# RDS Instance
# 

resource "aws_db_instance" "cloudaudit_rds" {
  identifier                  = "${var.APP_NAME}${var.env_suffix}"
  engine                      = "mysql"
  engine_version              = "8.4.5"
  instance_class              = "db.t3.micro"
  allocated_storage           = 20
  storage_encrypted           = true
  db_subnet_group_name        = aws_db_subnet_group.private_cloudaudit_sb_grp.name
  vpc_security_group_ids      = [aws_security_group.cloudaudit_rds_sg.id]
  publicly_accessible         = false
  skip_final_snapshot         = true
  availability_zone           = "us-east-1b"
  username                    = "admin"
  manage_master_user_password = true

  tags = {
    Name        = "${var.APP_NAME}${var.env_suffix}"
    Environment = "${var.env}"
    ManagedBy   = "Terraform"
  }
}

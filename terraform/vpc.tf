
# 
# VPC
# 

resource "aws_vpc" "cloudaudit_vpc" {
  cidr_block           = var.vpc_cidr
  enable_dns_support   = true
  enable_dns_hostnames = true
  tags = {
    Name        = "${var.APP_NAME}${var.env_suffix}-vpc"
    Environment = "${var.env}"
    ManagedBy   = "Terraform"
  }
}

# 
# Internet Gateway
# 

resource "aws_internet_gateway" "cloudaudit_igw" {
  vpc_id = aws_vpc.cloudaudit_vpc.id
  tags = {
    Name        = "${var.APP_NAME}${var.env_suffix}-igw"
    Environment = "${var.env}"
    ManagedBy   = "Terraform"
  }
}

# 
# Subnets
#

resource "aws_subnet" "public1" {
  vpc_id                  = aws_vpc.cloudaudit_vpc.id
  cidr_block              = var.public1_cidr
  availability_zone       = "us-east-1a"
  map_public_ip_on_launch = true
  tags = {
    Name        = "${var.APP_NAME}${var.env_suffix}-subnet-public1-us-east-1a"
    Environment = "${var.env}"
    ManagedBy   = "Terraform"
  }
}

resource "aws_subnet" "private1" {
  vpc_id            = aws_vpc.cloudaudit_vpc.id
  cidr_block        = var.private1_cidr
  availability_zone = "us-east-1a"
  tags = {
    Name        = "${var.APP_NAME}${var.env_suffix}-subnet-private1-us-east-1a"
    Environment = "${var.env}"
    ManagedBy   = "Terraform"
  }
}

resource "aws_subnet" "public2" {
  vpc_id                  = aws_vpc.cloudaudit_vpc.id
  cidr_block              = var.public2_cidr
  availability_zone       = "us-east-1b"
  map_public_ip_on_launch = true
  tags = {
    Name        = "${var.APP_NAME}${var.env_suffix}-subnet-public2-us-east-1b"
    Environment = "${var.env}"
    ManagedBy   = "Terraform"
  }
}

resource "aws_subnet" "private2" {
  vpc_id            = aws_vpc.cloudaudit_vpc.id
  cidr_block        = var.private2_cidr
  availability_zone = "us-east-1b"
  tags = {
    Name        = "${var.APP_NAME}${var.env_suffix}-subnet-private2-us-east-1b"
    Environment = "${var.env}"
    ManagedBy   = "Terraform"
  }
}

# 
# Route Tables
# 

resource "aws_route_table" "public" {
  vpc_id = aws_vpc.cloudaudit_vpc.id
  tags = {
    Name        = "${var.APP_NAME}${var.env_suffix}-rtb-public"
    Environment = "${var.env}"
    ManagedBy   = "Terraform"
  }
}

# Public Route

resource "aws_route" "public_internet" {
  route_table_id         = aws_route_table.public.id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = aws_internet_gateway.cloudaudit_igw.id
}

# Private RTB 1

resource "aws_route_table" "private1" {
  vpc_id = aws_vpc.cloudaudit_vpc.id
  tags = {
    Name        = "${var.APP_NAME}${var.env_suffix}-rtb-private1-us-east-1a"
    Environment = "${var.env}"
    ManagedBy   = "Terraform"
  }
}

# Private RTB 2

resource "aws_route_table" "private2" {
  vpc_id = aws_vpc.cloudaudit_vpc.id
  tags = {
    Name        = "${var.APP_NAME}${var.env_suffix}-rtb-private2-us-east-1b"
    Environment = "${var.env}"
    ManagedBy   = "Terraform"
  }
}

# Private Route
resource "aws_route" "private1_internet" {
  route_table_id         = aws_route_table.private1.id
  destination_cidr_block = "0.0.0.0/0"
  nat_gateway_id         = aws_nat_gateway.nat1.id
}

resource "aws_route" "private2_internet" {
  route_table_id         = aws_route_table.private2.id
  destination_cidr_block = "0.0.0.0/0"
  nat_gateway_id         = aws_nat_gateway.nat1.id
}

# 
# Route Table Associations
# 

resource "aws_route_table_association" "public1_assoc" {
  subnet_id      = aws_subnet.public1.id
  route_table_id = aws_route_table.public.id
}

resource "aws_route_table_association" "public2_assoc" {
  subnet_id      = aws_subnet.public2.id
  route_table_id = aws_route_table.public.id
}

resource "aws_route_table_association" "private1_assoc" {
  subnet_id      = aws_subnet.private1.id
  route_table_id = aws_route_table.private1.id
}

resource "aws_route_table_association" "private2_assoc" {
  subnet_id      = aws_subnet.private2.id
  route_table_id = aws_route_table.private2.id
}

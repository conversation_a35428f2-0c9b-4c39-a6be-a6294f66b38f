
# 
# Security Group
# 

resource "aws_security_group" "alb_sg" {
  name        = "${var.APP_NAME}${var.env_suffix}-alb-sg"
  description = "Security group for CloudAudit ALB"
  vpc_id      = aws_vpc.cloudaudit_vpc.id

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "${var.APP_NAME}${var.env_suffix}-alb-sg"
    Environment = "${var.env}"
    ManagedBy   = "Terraform"
  }
}

resource "aws_security_group" "cloudaudit_ec2_sg" {
  name        = "${var.APP_NAME}${var.env_suffix}-ec2-sg"
  description = "Security group for CloudAudit EC2"
  vpc_id      = aws_vpc.cloudaudit_vpc.id

  # Inbound rules
  ingress {
    description     = "Allow HTTP from ALB"
    from_port       = 80
    to_port         = 80
    protocol        = "tcp"
    security_groups = [aws_security_group.alb_sg.id]
  }

  # Outbound rules
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "${var.APP_NAME}${var.env_suffix}-ec2-sg"
    Environment = "${var.env}"
    ManagedBy   = "Terraform"
  }
}

# 
# EC2 Instance
# 

resource "aws_instance" "cloudaudit" {
  ami                         = "ami-020cba7c55df1f615"
  instance_type               = "t2.medium"
  subnet_id                   = aws_subnet.private2.id
  vpc_security_group_ids      = [aws_security_group.cloudaudit_ec2_sg.id]
  associate_public_ip_address = false
  root_block_device {
    delete_on_termination = false
    tags = {
      Name = "${var.APP_NAME}${var.env_suffix}"
    }
  }

  tags = {
    Name        = "${var.APP_NAME}${var.env_suffix}"
    Environment = "${var.env}"
    ManagedBy   = "Terraform"
  }
}

# 
# Elastic IP
# 

resource "aws_eip" "nat1" {
  domain = "vpc"

  tags = {
    Name        = "${var.APP_NAME}${var.env_suffix}-nat-eip-1a"
    Environment = "${var.env}"
    ManagedBy   = "Terraform"
  }
}

#
# NAT Gateway 
#

resource "aws_nat_gateway" "nat1" {
  allocation_id = aws_eip.nat1.id
  subnet_id     = aws_subnet.public1.id

  tags = {
    Name        = "${var.APP_NAME}${var.env_suffix}-natgw-1a"
    Environment = "${var.env}"
    ManagedBy   = "Terraform"
  }
}
resource "aws_lb" "cloudaudit_alb" {
  name               = "${var.APP_NAME}${var.env_suffix}-alb"
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb_sg.id]
  subnets            = [aws_subnet.public1.id, aws_subnet.public2.id]

  tags = {
    Name = "${var.APP_NAME}${var.env_suffix}-alb"
  }
}

resource "aws_lb_target_group" "cloudaudit_tg" {
  name     = "${var.APP_NAME}${var.env_suffix}-tg"
  port     = 80
  protocol = "HTTP"
  vpc_id   = aws_vpc.cloudaudit_vpc.id
}

resource "aws_lb_listener" "https_listener" {
  load_balancer_arn = aws_lb.cloudaudit_alb.arn
  port              = 443
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-2016-08"
  certificate_arn   = var.acm_cert_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.cloudaudit_tg.arn
  }
}

resource "aws_lb_listener" "http_listener" {
  load_balancer_arn = aws_lb.cloudaudit_alb.arn
  port              = 80
  protocol          = "HTTP"

  default_action {
    type = "redirect"

    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

resource "aws_lb_target_group_attachment" "cloudaudit_ec2_attach" {
  target_group_arn = aws_lb_target_group.cloudaudit_tg.arn
  target_id        = aws_instance.cloudaudit.id
  port             = 80
}

import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  User,
  Mail,
  Save,
  AlertCircle,
  CheckCircle,
  Lock,
  Eye,
  EyeOff,
} from "lucide-react";
import { motion } from "framer-motion";
import toast from "react-hot-toast";

import { useAuth } from "@/hooks/useAuth";
import {
  useUpdateUserInfo,
  useUpdatePassword,
} from "@/hooks/useUserManagement";
import {
  updateUserInfoSchema,
  UpdateUserInfoFormValues,
  updatePasswordSchema,
  UpdatePasswordFormValues,
} from "@/schemas/userManagement";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/Card";
import Modal from "@/components/ui/Modal";

const UserProfile: React.FC = () => {
  const { user, isLoading: isUserLoading } = useAuth();
  const updateUserInfoMutation = useUpdateUserInfo();
  const updatePasswordMutation = useUpdatePassword();
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [pendingData, setPendingData] =
    useState<UpdateUserInfoFormValues | null>(null);
  const [pendingPasswordData, setPendingPasswordData] =
    useState<UpdatePasswordFormValues | null>(null);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Form setup with Zod validation
  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
    watch,
  } = useForm<UpdateUserInfoFormValues>({
    resolver: zodResolver(updateUserInfoSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
    },
  });

  // Watch form values for real-time validation feedback
  const watchedValues = watch();

  // Password form setup with Zod validation
  const {
    register: registerPassword,
    handleSubmit: handleSubmitPassword,
    formState: { errors: passwordErrors },
    reset: resetPasswordForm,
  } = useForm<UpdatePasswordFormValues>({
    resolver: zodResolver(updatePasswordSchema),
    defaultValues: {
      current_password: "",
      new_password: "",
      confirm_password: "",
    },
  });

  // Pre-populate form with current user data
  useEffect(() => {
    if (user) {
      reset({
        first_name: user.first_name || "",
        last_name: user.last_name || "",
        email: user.email || "",
      });
    }
  }, [user, reset]);

  // Handle form submission with confirmation
  const onSubmit = (data: UpdateUserInfoFormValues) => {
    setPendingData(data);
    setShowConfirmModal(true);
  };

  // Handle confirmed update
  const handleConfirmedUpdate = async () => {
    if (!pendingData) return;

    try {
      await updateUserInfoMutation.mutateAsync(pendingData);

      // Show success toast
      toast.success("Profile updated successfully");

      // Reset form dirty state
      reset(pendingData);

      // Close modal
      setShowConfirmModal(false);
      setPendingData(null);
    } catch (error) {
      // Show error toast
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to update profile. Please try again."
      );
    }
  };

  // Handle keyboard navigation in modal
  const handleModalKeyDown = (e: React.KeyboardEvent) => {
    if (updateUserInfoMutation.isPending) return;

    if (e.key === "Enter" && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleConfirmedUpdate();
    }
  };

  // Handle modal close
  const handleModalClose = () => {
    if (updateUserInfoMutation.isPending) return; // Prevent closing during update
    setShowConfirmModal(false);
    setPendingData(null);
  };

  // Check if there are any changes
  const hasChanges =
    isDirty &&
    user &&
    (watchedValues.first_name !== (user.first_name || "") ||
      watchedValues.last_name !== (user.last_name || "") ||
      watchedValues.email !== (user.email || ""));

  // Helper function to get change summary
  const getChangeSummary = () => {
    if (!pendingData || !user) return [];

    const changes = [];
    if (user.first_name !== pendingData.first_name) {
      changes.push("First Name");
    }
    if (user.last_name !== pendingData.last_name) {
      changes.push("Last Name");
    }
    if (user.email !== pendingData.email) {
      changes.push("Email");
    }
    return changes;
  };

  // Password change handlers
  const onPasswordSubmit = (data: UpdatePasswordFormValues) => {
    setPendingPasswordData(data);
    setShowPasswordModal(true);
  };

  const handleConfirmedPasswordUpdate = async () => {
    if (!pendingPasswordData) return;

    try {
      await updatePasswordMutation.mutateAsync(pendingPasswordData);

      // Show success toast
      toast.success("Password updated successfully");

      // Reset form and clear data
      resetPasswordForm();
      setShowPasswordModal(false);
      setPendingPasswordData(null);

      // Reset password visibility states
      setShowCurrentPassword(false);
      setShowNewPassword(false);
      setShowConfirmPassword(false);
    } catch (error) {
      // Show error toast
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to update password. Please try again."
      );
    }
  };

  const handlePasswordModalClose = () => {
    if (updatePasswordMutation.isPending) return;
    setShowPasswordModal(false);
    setPendingPasswordData(null);
    resetPasswordForm();
    setShowCurrentPassword(false);
    setShowNewPassword(false);
    setShowConfirmPassword(false);
  };

  const handlePasswordModalKeyDown = (e: React.KeyboardEvent) => {
    if (updatePasswordMutation.isPending) return;

    if (e.key === "Enter" && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleConfirmedPasswordUpdate();
    }
  };

  // Show loading state while user data is being fetched
  if (isUserLoading) {
    return (
      <div className="max-w-2xl mx-auto space-y-6">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-dark-100 mb-2">
            Profile Settings
          </h1>
          <p className="text-dark-400">
            Manage your personal information and account settings.
          </p>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="animate-pulse space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="h-10 bg-dark-800 rounded"></div>
                <div className="h-10 bg-dark-800 rounded"></div>
              </div>
              <div className="h-10 bg-dark-800 rounded"></div>
              <div className="h-10 bg-dark-800 rounded w-32"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-dark-100 mb-2">
          Profile Settings
        </h1>
        <p className="text-dark-400">
          Manage your personal information and account settings.
        </p>
      </div>

      {/* Profile Information Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User size={20} />
            Personal Information
          </CardTitle>
          <CardDescription>
            Update your personal details. Changes will be reflected across your
            account.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Name Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="First Name"
                type="text"
                placeholder="Enter your first name"
                leftIcon={<User size={16} />}
                error={errors.first_name?.message}
                fullWidth
                {...register("first_name")}
              />

              <Input
                label="Last Name"
                type="text"
                placeholder="Enter your last name"
                leftIcon={<User size={16} />}
                error={errors.last_name?.message}
                fullWidth
                {...register("last_name")}
              />
            </div>

            {/* Email Field */}
            <Input
              label="Email Address"
              type="email"
              placeholder="Enter your email address"
              leftIcon={<Mail size={16} />}
              error={errors.email?.message}
              fullWidth
              {...register("email")}
            />

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-4 border-t border-dark-800">
              <div className="flex items-center gap-2 text-sm">
                {hasChanges && (
                  <motion.div
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="flex items-center gap-2 text-amber-400"
                  >
                    <AlertCircle size={16} />
                    <span>You have unsaved changes</span>
                  </motion.div>
                )}
              </div>

              <div className="flex items-center gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => reset()}
                  disabled={!hasChanges || updateUserInfoMutation.isPending}
                >
                  Reset
                </Button>

                <Button
                  type="submit"
                  leftIcon={<Save size={16} />}
                  disabled={!hasChanges || updateUserInfoMutation.isPending}
                  isLoading={updateUserInfoMutation.isPending}
                >
                  Save Changes
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Password Change Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lock size={20} />
            Change Password
          </CardTitle>
          <CardDescription>
            Update your password to keep your account secure. Make sure to use a
            strong password.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form
            onSubmit={handleSubmitPassword(onPasswordSubmit)}
            className="space-y-6"
          >
            {/* Current Password Field */}
            <Input
              label="Current Password"
              type={showCurrentPassword ? "text" : "password"}
              placeholder="Enter your current password"
              leftIcon={<Lock size={16} />}
              rightIcon={
                <button
                  type="button"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                  className="text-dark-400 hover:text-dark-200 transition-colors"
                  aria-label={
                    showCurrentPassword ? "Hide password" : "Show password"
                  }
                >
                  {showCurrentPassword ? (
                    <EyeOff size={16} />
                  ) : (
                    <Eye size={16} />
                  )}
                </button>
              }
              error={passwordErrors.current_password?.message}
              fullWidth
              {...registerPassword("current_password")}
            />

            {/* New Password Field */}
            <Input
              label="New Password"
              type={showNewPassword ? "text" : "password"}
              placeholder="Enter your new password"
              leftIcon={<Lock size={16} />}
              rightIcon={
                <button
                  type="button"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                  className="text-dark-400 hover:text-dark-200 transition-colors"
                  aria-label={
                    showNewPassword ? "Hide password" : "Show password"
                  }
                >
                  {showNewPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                </button>
              }
              error={passwordErrors.new_password?.message}
              fullWidth
              {...registerPassword("new_password")}
            />

            {/* Confirm New Password Field */}
            <Input
              label="Confirm New Password"
              type={showConfirmPassword ? "text" : "password"}
              placeholder="Confirm your new password"
              leftIcon={<Lock size={16} />}
              rightIcon={
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="text-dark-400 hover:text-dark-200 transition-colors"
                  aria-label={
                    showConfirmPassword ? "Hide password" : "Show password"
                  }
                >
                  {showConfirmPassword ? (
                    <EyeOff size={16} />
                  ) : (
                    <Eye size={16} />
                  )}
                </button>
              }
              error={passwordErrors.confirm_password?.message}
              fullWidth
              {...registerPassword("confirm_password")}
            />

            {/* Action Buttons */}
            <div className="flex items-center justify-end pt-4 border-t border-dark-800">
              <div className="flex items-center gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => resetPasswordForm()}
                  disabled={updatePasswordMutation.isPending}
                >
                  Reset
                </Button>

                <Button
                  type="submit"
                  leftIcon={<Lock size={16} />}
                  disabled={updatePasswordMutation.isPending}
                  isLoading={updatePasswordMutation.isPending}
                >
                  Change Password
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Enhanced Confirmation Modal */}
      <Modal
        isOpen={showConfirmModal}
        onClose={handleModalClose}
        title="Confirm Profile Update"
        size="lg"
        className="max-w-lg mx-4 w-full"
        showCloseButton={!updateUserInfoMutation.isPending}
      >
        <div
          className="p-4 sm:p-6 max-w-full overflow-hidden"
          onKeyDown={handleModalKeyDown}
        >
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-4 sm:space-y-6 max-w-full"
          >
            {/* Header Section with Icon and Message */}
            <div className="flex items-start gap-3 sm:gap-4 max-w-full">
              <motion.div
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.1, duration: 0.3 }}
                className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-primary-500/20 to-primary-600/20 rounded-xl flex items-center justify-center border border-primary-500/30"
              >
                <CheckCircle
                  size={20}
                  className="text-primary-400 sm:w-6 sm:h-6"
                />
              </motion.div>
              <div className="flex-1 pt-1 min-w-0 overflow-hidden">
                <h3 className="text-base sm:text-lg font-semibold text-dark-100 mb-2 break-words">
                  Review Your Changes
                </h3>
                <p className="text-dark-300 text-xs sm:text-sm leading-relaxed break-words">
                  Please review the changes below before updating your profile.
                  These changes will be reflected across your account
                  immediately.
                </p>
                {pendingData && (
                  <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mt-2 max-w-full">
                    <span className="text-xs text-dark-400 flex-shrink-0">
                      {getChangeSummary().length} field
                      {getChangeSummary().length !== 1 ? "s" : ""} will be
                      updated:
                    </span>
                    <span className="text-xs text-primary-400 font-medium break-words min-w-0">
                      {getChangeSummary().join(", ")}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Changes Summary */}
            {pendingData && user && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.3 }}
                className="bg-gradient-to-br from-dark-800/80 to-dark-900/80 rounded-xl p-3 sm:p-4 border border-dark-700/50 backdrop-blur-sm max-w-full overflow-hidden"
              >
                <h4 className="text-xs sm:text-sm font-medium text-dark-200 mb-3 flex items-center gap-2">
                  <User
                    size={14}
                    className="text-primary-400 flex-shrink-0 sm:w-4 sm:h-4"
                  />
                  <span className="truncate">Profile Changes</span>
                </h4>
                <div className="space-y-2 sm:space-y-3 max-w-full">
                  {/* First Name Change */}
                  <div className="py-2 px-2 sm:px-3 bg-dark-800/50 rounded-lg border border-dark-700/30 max-w-full overflow-hidden">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs sm:text-sm text-dark-400 font-medium flex-shrink-0">
                        First Name
                      </span>
                    </div>
                    <div className="flex items-center gap-2 min-w-0 overflow-hidden">
                      {user.first_name !== pendingData.first_name && (
                        <>
                          <span
                            className="text-xs text-dark-500 line-through truncate flex-1 min-w-0"
                            title={user.first_name || "Not set"}
                          >
                            {user.first_name || "Not set"}
                          </span>
                          <span className="text-xs text-dark-400 flex-shrink-0">
                            →
                          </span>
                        </>
                      )}
                      <span
                        className="text-xs sm:text-sm text-dark-100 font-medium truncate flex-1 min-w-0"
                        title={pendingData.first_name}
                      >
                        {pendingData.first_name}
                      </span>
                    </div>
                  </div>

                  {/* Last Name Change */}
                  <div className="py-2 px-2 sm:px-3 bg-dark-800/50 rounded-lg border border-dark-700/30 max-w-full overflow-hidden">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs sm:text-sm text-dark-400 font-medium flex-shrink-0">
                        Last Name
                      </span>
                    </div>
                    <div className="flex items-center gap-2 min-w-0 overflow-hidden">
                      {user.last_name !== pendingData.last_name && (
                        <>
                          <span
                            className="text-xs text-dark-500 line-through truncate flex-1 min-w-0"
                            title={user.last_name || "Not set"}
                          >
                            {user.last_name || "Not set"}
                          </span>
                          <span className="text-xs text-dark-400 flex-shrink-0">
                            →
                          </span>
                        </>
                      )}
                      <span
                        className="text-xs sm:text-sm text-dark-100 font-medium truncate flex-1 min-w-0"
                        title={pendingData.last_name}
                      >
                        {pendingData.last_name}
                      </span>
                    </div>
                  </div>

                  {/* Email Change - Fixed Layout */}
                  <div className="py-2 px-2 sm:px-3 bg-dark-800/50 rounded-lg border border-dark-700/30 max-w-full overflow-hidden">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs sm:text-sm text-dark-400 font-medium flex-shrink-0">
                        Email
                      </span>
                    </div>
                    {/* Stack emails vertically for better readability */}
                    <div className="space-y-1">
                      {user.email !== pendingData.email && user.email && (
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-dark-500 flex-shrink-0">
                            From:
                          </span>
                          <span
                            className="text-xs text-dark-500 line-through truncate flex-1 min-w-0"
                            title={user.email}
                          >
                            {user.email}
                          </span>
                        </div>
                      )}
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-dark-400 flex-shrink-0">
                          {user.email !== pendingData.email ? "To:" : ""}
                        </span>
                        <span
                          className="text-xs sm:text-sm text-dark-100 font-medium truncate flex-1 min-w-0"
                          title={pendingData.email}
                        >
                          {pendingData.email}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
            {/* Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.3 }}
              className="flex flex-col sm:flex-row items-stretch sm:items-center justify-end gap-2 sm:gap-3 pt-2 max-w-full"
            >
              <Button
                variant="ghost"
                size="md"
                onClick={handleModalClose}
                disabled={updateUserInfoMutation.isPending}
                className="w-full sm:w-auto sm:min-w-[100px] hover:bg-dark-700/50 focus:ring-2 focus:ring-dark-500 focus:ring-offset-2 focus:ring-offset-dark-800 order-2 sm:order-1"
                aria-label="Cancel profile update"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                size="md"
                onClick={handleConfirmedUpdate}
                isLoading={updateUserInfoMutation.isPending}
                leftIcon={
                  !updateUserInfoMutation.isPending ? (
                    <Save size={14} className="sm:w-4 sm:h-4" />
                  ) : undefined
                }
                className="w-full sm:w-auto sm:min-w-[140px] bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 shadow-lg shadow-primary-500/25 focus:ring-2 focus:ring-primary-400 focus:ring-offset-2 focus:ring-offset-dark-800 order-1 sm:order-2"
                disabled={updateUserInfoMutation.isPending}
                aria-label={
                  updateUserInfoMutation.isPending
                    ? "Updating profile..."
                    : "Confirm and update profile"
                }
                autoFocus
              >
                <span className="truncate">
                  {updateUserInfoMutation.isPending
                    ? "Updating..."
                    : "Update Profile"}
                </span>
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </Modal>

      {/* Password Change Confirmation Modal */}
      <Modal
        isOpen={showPasswordModal}
        onClose={handlePasswordModalClose}
        title="Confirm Password Change"
        size="lg"
        className="max-w-lg mx-4 w-full"
        showCloseButton={!updatePasswordMutation.isPending}
      >
        <div
          className="p-4 sm:p-6 max-w-full overflow-hidden"
          onKeyDown={handlePasswordModalKeyDown}
        >
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-4 sm:space-y-6 max-w-full"
          >
            {/* Header Section with Icon and Message */}
            <div className="flex items-start gap-3 sm:gap-4 max-w-full">
              <motion.div
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.1, duration: 0.3 }}
                className="flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-amber-500/20 to-amber-600/20 rounded-xl flex items-center justify-center border border-amber-500/30"
              >
                <Lock size={20} className="text-amber-400 sm:w-6 sm:h-6" />
              </motion.div>
              <div className="flex-1 pt-1 min-w-0 overflow-hidden">
                <h3 className="text-base sm:text-lg font-semibold text-dark-100 mb-2 break-words">
                  Confirm Password Change
                </h3>
                <p className="text-dark-300 text-xs sm:text-sm leading-relaxed break-words">
                  Are you sure you want to change your password? This action
                  will update your account security credentials.
                </p>
              </div>
            </div>

            {/* Security Notice */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.3 }}
              className="bg-gradient-to-br from-amber-500/10 to-amber-600/10 rounded-xl p-3 sm:p-4 border border-amber-500/20 backdrop-blur-sm max-w-full overflow-hidden"
            >
              <div className="flex items-start gap-2">
                <AlertCircle
                  size={16}
                  className="text-amber-400 flex-shrink-0 mt-0.5"
                />
                <div className="text-xs sm:text-sm text-amber-200">
                  <p className="font-medium mb-1">Security Notice:</p>
                  <ul className="space-y-1 text-amber-300/80">
                    <li>• Your password will be updated immediately</li>
                    <li>• You will remain logged in on this device</li>
                    <li>• Other sessions may require re-authentication</li>
                  </ul>
                </div>
              </div>
            </motion.div>

            {/* Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.3 }}
              className="flex flex-col sm:flex-row items-stretch sm:items-center justify-end gap-2 sm:gap-3 pt-2 max-w-full"
            >
              <Button
                variant="ghost"
                size="md"
                onClick={handlePasswordModalClose}
                disabled={updatePasswordMutation.isPending}
                className="w-full sm:w-auto sm:min-w-[100px] hover:bg-dark-700/50 focus:ring-2 focus:ring-dark-500 focus:ring-offset-2 focus:ring-offset-dark-800 order-2 sm:order-1"
                aria-label="Cancel password change"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                size="md"
                onClick={handleConfirmedPasswordUpdate}
                isLoading={updatePasswordMutation.isPending}
                leftIcon={
                  !updatePasswordMutation.isPending ? (
                    <Lock size={14} className="sm:w-4 sm:h-4" />
                  ) : undefined
                }
                className="w-full sm:w-auto sm:min-w-[140px] bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 shadow-lg shadow-amber-500/25 focus:ring-2 focus:ring-amber-400 focus:ring-offset-2 focus:ring-offset-dark-800 order-1 sm:order-2"
                disabled={updatePasswordMutation.isPending}
                aria-label={
                  updatePasswordMutation.isPending
                    ? "Changing password..."
                    : "Confirm and change password"
                }
                autoFocus
              >
                <span className="truncate">
                  {updatePasswordMutation.isPending
                    ? "Changing..."
                    : "Change Password"}
                </span>
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </Modal>
    </div>
  );
};

export default UserProfile;

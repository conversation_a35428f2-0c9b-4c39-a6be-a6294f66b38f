import { useInfiniteFindings, useScanDetails } from "@/hooks/useScans";
import { formatDate } from "@/lib/dateUtils";
import { ApiFinding } from "@/types/api";
import CustomBreadcrumbs from "@components/CustomBreadcrumbs";
import ScanResultItem from "@components/ScanResultItem";
import Button from "@components/ui/Button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@components/ui/Card";
import { AlertCircle, CheckCircle, Filter, Loader2 } from "lucide-react";
import React, { useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";

type FilterOptions = {
  severity: string[];
  status: string[];
  service: string[];
  region: string[];
};

const ScanFindings: React.FC = () => {
  const { scanId } = useParams<{ scanId: string }>();
  const numericScanId = scanId ? parseInt(scanId, 10) : null;
  const [searchParams] = useSearchParams();
  const serviceName = searchParams.get("service");
  const navigate = useNavigate();

  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<FilterOptions>({
    severity: [],
    status: [],
    service: [],
    region: [],
  });

  // Fetch scan details
  const {
    data: scanDetailsData,
    isLoading: isLoadingScanDetails,
    error: scanDetailsError,
  } = useScanDetails(numericScanId);

  // Find the service ID based on the service name from URL
  const selectedService = scanDetailsData?.data?.services?.find(
    (service) =>
      service.service_name.toLowerCase() === serviceName?.toLowerCase()
  );
  const selectedServiceId = selectedService?.service_id || null;

  // We'll handle all filtering client-side to avoid unnecessary API calls
  // Previously, status filtering was done server-side, but this caused API calls on every filter change
  // Now we fetch all findings once and filter them client-side for both severity and status
  const apiFilters = React.useMemo(() => {
    return {};
  }, []);

  // Fetch findings for the selected service without filters
  const {
    data: infiniteFindingsData,
    isLoading: isLoadingFindings,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteFindings(numericScanId, selectedServiceId, 20, apiFilters);

  // Handle loading state
  if (isLoadingScanDetails) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="w-8 h-8 text-primary-500 animate-spin" />
        <p className="ml-2 text-dark-400">Loading scan details...</p>
      </div>
    );
  }

  // Handle error state
  if (scanDetailsError || !scanDetailsData) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-error-500">Error loading scan details</p>
      </div>
    );
  }

  const scanDetails = scanDetailsData?.data;

  // Handle case when service is not found
  if (!selectedServiceId && serviceName) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <p className="text-error-500 mb-4">
          Service "{serviceName}" not found in this scan
        </p>
        {/* <Button
          variant="outline"
          onClick={() => navigate(`/dashboard/scans/${scanId}`)}
          leftIcon={<ArrowLeft size={16} />}
        >
          Back to Scan Details
        </Button> */}
      </div>
    );
  }

  // Extract all findings from all pages
  const allFindings =
    infiniteFindingsData?.pages.flatMap((page) => page.data) || [];

  // Apply client-side filters to findings (both severity and status)
  const filteredFindings = allFindings.filter((finding: ApiFinding) => {
    // Filter by severity if severity filters are selected
    if (
      filters.severity.length > 0 &&
      !filters.severity.includes(finding.severity)
    ) {
      return false;
    }

    // Filter by status if status filters are selected
    if (filters.status.length > 0 && !filters.status.includes(finding.status)) {
      return false;
    }
    return true;
  });

  const toggleFilter = (type: keyof FilterOptions, value: string) => {
    setFilters((prev) => {
      const currentFilters = [...prev[type]];
      const index = currentFilters.indexOf(value);

      if (index >= 0) {
        currentFilters.splice(index, 1);
      } else {
        currentFilters.push(value);
      }

      return {
        ...prev,
        [type]: currentFilters,
      };
    });
  };

  const clearFilters = () => {
    setFilters({
      severity: [],
      status: [],
      service: [],
      region: [],
    });
  };

  return (
    <div className="space-y-8">
      {/* Custom breadcrumbs that preserve the service parameter */}
      <CustomBreadcrumbs type="findings" />

      {/* Back button and title */}
      <div className="flex items-center gap-4">
        {/* <Button
          variant="outline"
          size="sm"
          leftIcon={<ArrowLeft size={16} />}
          onClick={() => navigate(`/dashboard/scans/${scanId}`)}
        >
          Back to Scan Details
        </Button> */}
        <div>
          <h1 className="text-2xl font-bold text-dark-100">
            {selectedService?.service_name} Findings
          </h1>
          <p className="text-dark-400">
            Scan #{scanDetails.id} • {scanDetails.account_name}
          </p>
        </div>
      </div>

      {/* Service Summary */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <h3 className="text-dark-400 text-sm mb-1">Service</h3>
              <p className="font-medium text-dark-200">
                {selectedService?.service_name}
              </p>
            </div>

            <div>
              <h3 className="text-dark-400 text-sm mb-1">Cloud Provider</h3>
              <p className="text-dark-200">
                {String(scanDetails?.cloud_provider)}
              </p>
            </div>

            <div>
              <h3 className="text-dark-400 text-sm mb-1">Scan Date</h3>
              <p className="text-dark-200">
                {formatDate(scanDetails?.scan_start)}
              </p>
            </div>

            <div>
              <h3 className="text-dark-400 text-sm mb-1">Status</h3>
              <div
                className={`px-2 py-1 rounded-full inline-block text-xs ${
                  selectedService?.status === "completed"
                    ? "bg-success-500/20 text-success-500"
                    : selectedService?.status === "running"
                    ? "bg-warning-500/20 text-warning-500"
                    : "bg-error-500/20 text-error-500"
                }`}
              >
                {selectedService?.status === "completed"
                  ? "Completed"
                  : selectedService?.status === "running"
                  ? "Running"
                  : "Failed"}
              </div>
            </div>

            <div className="col-span-full">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 border border-dark-700 rounded-lg p-4 bg-dark-850">
                <div className="flex items-center gap-3">
                  <div className="bg-dark-800 p-2 rounded-full">
                    <Filter size={18} className="text-dark-400" />
                  </div>
                  <div>
                    <h3 className="text-dark-300">Total Findings</h3>
                    <p className="text-xl font-semibold text-dark-100">
                      {selectedService?.findings_count || 0}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <div className="bg-success-500/20 p-2 rounded-full">
                    <CheckCircle size={18} className="text-success-500" />
                  </div>
                  <div>
                    <h3 className="text-dark-300">Passed</h3>
                    <p className="text-xl font-semibold text-success-500">
                      {selectedService?.passed_findings || 0}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <div className="bg-error-500/20 p-2 rounded-full">
                    <AlertCircle size={18} className="text-error-500" />
                  </div>
                  <div>
                    <h3 className="text-dark-300">Failed</h3>
                    <p className="text-xl font-semibold text-error-500">
                      {selectedService?.failed_findings || 0}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Findings */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-5">
          <div>
            <CardTitle>Findings</CardTitle>
            <CardDescription>
              Detailed findings for {selectedService?.service_name}
            </CardDescription>
          </div>

          <Button
            variant={showFilters ? "primary" : "outline"}
            size="sm"
            leftIcon={<Filter size={16} />}
            onClick={() => setShowFilters(!showFilters)}
            className="ml-4"
          >
            {showFilters ? "Hide Filters" : "Show Filters"}
          </Button>
        </CardHeader>

        {showFilters && (
          <div className="px-6 pt-4 pb-6 border-b border-dark-700 bg-dark-850">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-2">
              <div>
                <h3 className="font-medium text-dark-200 mb-3">Severity</h3>
                <div className="space-y-3">
                  {["critical", "high", "medium", "low"].map((severity) => (
                    <label
                      key={severity}
                      className="flex items-center gap-2 cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        className="rounded border-dark-600 text-primary-500 focus:ring-primary-500"
                        checked={filters.severity.includes(severity)}
                        onChange={() => toggleFilter("severity", severity)}
                      />
                      <span className="text-dark-300 capitalize">
                        {severity}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="font-medium text-dark-200 mb-3">Status</h3>
                <div className="space-y-3">
                  {["pass", "fail", "remediated"].map((status) => (
                    <label
                      key={status}
                      className="flex items-center gap-2 cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        className="rounded border-dark-600 text-primary-500 focus:ring-primary-500"
                        checked={filters.status.includes(status)}
                        onChange={() => toggleFilter("status", status)}
                      />
                      <span className="text-dark-300 capitalize">{status}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex justify-end mt-6">
              <Button variant="outline" size="sm" onClick={clearFilters}>
                Clear Filters
              </Button>
            </div>
          </div>
        )}

        <CardContent className="pt-6 mt-2">
          {isLoadingFindings && !infiniteFindingsData ? (
            <div className="py-12 text-center">
              <Loader2 className="w-8 h-8 text-primary-500 animate-spin mx-auto mb-4" />
              <p className="text-dark-400">Loading findings...</p>
            </div>
          ) : filteredFindings.length > 0 ? (
            <div>
              {filteredFindings.map((finding) => (
                <ScanResultItem
                  key={finding.id}
                  finding={{
                    description: finding.description,
                    service: finding.service_name,
                    region: "", // API doesn't provide region in findings
                    severity: finding.severity,
                    status: finding.status,
                    lastSeen: finding.created_at,
                  }}
                  onClick={() =>
                    navigate(
                      `/dashboard/scans/${scanId}/findings/${finding.id}?service=${serviceName}`
                    )
                  }
                />
              ))}

              {hasNextPage && (
                <div className="flex justify-center mt-6">
                  <Button
                    variant="outline"
                    onClick={() => fetchNextPage()}
                    disabled={isFetchingNextPage}
                    leftIcon={
                      isFetchingNextPage ? (
                        <Loader2 className="animate-spin" />
                      ) : undefined
                    }
                  >
                    {isFetchingNextPage ? "Loading more..." : "Load More"}
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="py-12 text-center">
              <p className="text-dark-400">
                {Object.values(filters).some((f) => f.length > 0)
                  ? "No findings match your filters."
                  : "No findings available for this service."}
              </p>
              {Object.values(filters).some((f) => f.length > 0) && (
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={clearFilters}
                >
                  Clear Filters
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ScanFindings;

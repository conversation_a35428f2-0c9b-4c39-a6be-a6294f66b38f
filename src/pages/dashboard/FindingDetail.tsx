import { useFindingDetail } from "@/hooks/useScans";
import CustomBreadcrumbs from "@components/CustomBreadcrumbs";
import {
  ErrorState,
  FindingDetailsCard,
  FindingSummary,
  LoadingState,
} from "@components/findings";
import React, { useCallback } from "react";
import { useNavigate, useParams } from "react-router-dom";

/**
 * FindingDetail page component
 *
 * Displays detailed information about a specific finding including:
 * - Summary information (service, account, cloud provider, date)
 * - Detailed information with remediation options
 *
 * Handles loading states, error handling, and data fetching
 */
const FindingDetail: React.FC = () => {
  const navigate = useNavigate();

  // Get finding ID from URL parameters
  const { findingId } = useParams<{
    findingId: string;
    scanId: string;
  }>();
  const numericFindingId = findingId ? parseInt(findingId, 10) : null;

  // Fetch finding details with React Query
  const {
    data: findingDetailData,
    isLoading,
    error,
    refetch,
  } = useFindingDetail(numericFindingId);

  // Handle back button click
  const handleBack = useCallback(() => {
    navigate(-1);
  }, [navigate]);

  // Handle retry button click
  const handleRetry = useCallback(() => {
    refetch();
  }, [refetch]);

  // Handle loading state
  if (isLoading) {
    return <LoadingState />;
  }

  // Handle error state
  if (error || !findingDetailData) {
    return (
      <ErrorState error={error} onRetry={handleRetry} onBack={handleBack} />
    );
  }

  // Extract finding data
  const finding = findingDetailData.data;

  return (
    <div className="space-y-8">
      {/* Custom breadcrumbs that preserve the service parameter */}
      <CustomBreadcrumbs type="findingDetail" />

      {/* Header with back button and title */}
      <div className="flex items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-dark-100">Finding Details</h1>
          <p className="text-dark-400">
            {finding.policy_check} • {finding.service_name}
          </p>
        </div>
      </div>

      {/* Finding Summary */}
      <FindingSummary finding={finding} />

      {/* Finding Details */}
      <FindingDetailsCard finding={finding} />
    </div>
  );
};

export default FindingDetail;

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import {
  Building,
  EyeIcon,
  EyeOffIcon,
  Lock,
  Mail,
  AlertCircle,
  User,
} from "lucide-react";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { Link } from "react-router-dom";
import { z } from "zod";
import Button from "@components/ui/Button";
import Input from "@components/ui/Input";
import { useAuth } from "@/hooks/useAuth";

const schema = z.object({
  first_name: z
    .string()
    .min(1, "First name is required")
    .max(50, "First name must be less than 50 characters"),
  last_name: z
    .string()
    .min(1, "Last name is required")
    .max(50, "Last name must be less than 50 characters"),
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  workspace: z.string().min(3, "Workspace name must be at least 3 characters"),
});

type FormValues = z.infer<typeof schema>;

const SignUp: React.FC = () => {
  const { signup } = useAuth();
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      password: "",
      workspace: "",
    },
  });

  const onSubmit = async (data: FormValues) => {
    try {
      await signup.mutateAsync({
        first_name: data.first_name,
        last_name: data.last_name,
        email: data.email,
        password: data.password,
        workspace_name: data.workspace,
      });
    } catch (error) {
      // Error is already handled in the mutation
      console.error("Signup error:", error);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-dark-100 mb-2">
          Create your account
        </h2>
        <p className="text-dark-400">Get started with CloudAudit</p>
      </div>

      {signup.error && (
        <div className="mb-6 p-3 bg-error-500/10 border border-error-500/30 rounded-md text-error-400 text-sm flex items-start">
          <AlertCircle size={16} className="mr-2 mt-0.5 flex-shrink-0" />
          <span>{signup.error.message}</span>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="First Name"
            type="text"
            placeholder="John"
            leftIcon={<User size={16} />}
            error={errors.first_name?.message}
            fullWidth
            {...register("first_name")}
          />

          <Input
            label="Last Name"
            type="text"
            placeholder="Doe"
            leftIcon={<User size={16} />}
            error={errors.last_name?.message}
            fullWidth
            {...register("last_name")}
          />
        </div>

        <Input
          label="Email"
          type="email"
          placeholder="<EMAIL>"
          leftIcon={<Mail size={16} />}
          error={errors.email?.message}
          fullWidth
          {...register("email")}
        />

        <Input
          label="Password"
          type={showPassword ? "text" : "password"}
          placeholder="••••••••"
          leftIcon={<Lock size={16} />}
          rightIcon={
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="focus:outline-none"
            >
              {showPassword ? <EyeOffIcon size={16} /> : <EyeIcon size={16} />}
            </button>
          }
          error={errors.password?.message}
          fullWidth
          {...register("password")}
        />

        <Input
          label="Workspace Name"
          type="text"
          placeholder="Your Company"
          leftIcon={<Building size={16} />}
          error={errors.workspace?.message}
          fullWidth
          {...register("workspace")}
        />

        <div>
          <Button
            type="submit"
            variant="primary"
            isLoading={signup.isPending}
            fullWidth
          >
            Create Account
          </Button>
        </div>
      </form>

      <div className="mt-8 text-center">
        <p className="text-dark-400">
          Already have an account?{" "}
          <Link
            to="/login"
            className="text-primary-500 hover:text-primary-400 transition-colors"
          >
            Sign in
          </Link>
        </p>
      </div>
    </motion.div>
  );
};

export default SignUp;

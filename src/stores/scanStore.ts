import { create } from 'zustand';
import { persist } from 'zustand/middleware';

/**
 * Scan state interface
 * This is a minimal store that only manages the last used account ID for scans
 * All other scan-related functionality has been migrated to React Query
 */
interface ScanState {
    lastUsedAccountId: string | null;
    setLastUsedAccountId: (accountId: string) => void;
    clearLastUsedAccountId: () => void;
}

/**
 * Scan store for managing scan preferences
 * This store is kept minimal and focused on user preferences only
 * All data fetching and caching is handled by React Query
 */
export const useScanStore = create<ScanState>()(
    persist(
        (set) => ({
            lastUsedAccountId: null,
            setLastUsedAccountId: (accountId) => set({ lastUsedAccountId: accountId }),
            clearLastUsedAccountId: () => set({ lastUsedAccountId: null }),
        }),
        {
            name: 'scan-store',
        }
    )
);

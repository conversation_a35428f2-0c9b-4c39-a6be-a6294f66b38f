import axios from 'axios';
import type { AuthResponse } from '../types/api';
import { ServiceError, ServiceErrorType } from './baseService';

// Token management functions (duplicated from baseService to avoid circular dependency)
const getRefreshToken = (): string | null => {
    return localStorage.getItem('refresh_token');
};

const setTokens = (accessToken: string, refreshToken: string): void => {
    localStorage.setItem('access_token', accessToken);
    localStorage.setItem('refresh_token', refreshToken);
};

const clearTokens = (): void => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user_email');
    localStorage.removeItem('workspace_name');
};

// Environment configuration
const API_URL = import.meta.env.VITE_BACKEND_LOCAL_BASE_URL || 'http://localhost:8000/api';

// Create a simple axios instance for token refresh (without interceptors to avoid circular dependency)
const createTokenRefreshApi = () => {
    return axios.create({
        baseURL: API_URL,
        headers: {
            'Content-Type': 'application/json'
        },
        timeout: 30000
    });
};

// A single promise for token refresh to prevent multiple refresh calls
let tokenRefreshPromise: Promise<string> | null = null;

export const refreshToken = async (): Promise<string> => {
    // If a refresh is already in progress, return the existing promise
    if (tokenRefreshPromise) {
        return tokenRefreshPromise;
    }

    // Create a new refresh token promise
    tokenRefreshPromise = (async () => {
        try {
            // Get the refresh token from storage
            const refreshTokenValue = getRefreshToken();
            if (!refreshTokenValue) {
                throw new ServiceError(
                    ServiceErrorType.REFRESH_FAILED,
                    'No refresh token available'
                );
            }

            // Create API instance for token refresh
            const api = createTokenRefreshApi();

            // Make the refresh token request
            const response = await api.post<AuthResponse>('/api/refresh-token', {
                refresh_token: refreshTokenValue
            });

            // Validate the response
            if (!response.data.access_token || !response.data.refresh_token) {
                throw new ServiceError(
                    ServiceErrorType.REFRESH_FAILED,
                    'Invalid token response from server'
                );
            }

            // Store the new tokens
            setTokens(response.data.access_token, response.data.refresh_token);

            // Return the new access token
            return response.data.access_token;
        } catch (error) {
            // Handle specific error cases
            if (error instanceof ServiceError) {
                // If it's already a ServiceError, just rethrow it
                clearTokens();
                throw error;
            } else if (axios.isAxiosError(error)) {
                // Handle network or server errors
                const axiosError = error;
                const status = axiosError.response?.status;

                if (status === 401 || status === 403) {
                    clearTokens();
                    throw new ServiceError(
                        ServiceErrorType.UNAUTHORIZED,
                        'Your session has expired. Please sign in again.',
                        error,
                        status
                    );
                } else {
                    clearTokens();
                    throw new ServiceError(
                        ServiceErrorType.REFRESH_FAILED,
                        'Failed to refresh authentication. Please sign in again.',
                        error,
                        status
                    );
                }
            } else {
                // Handle unknown errors
                clearTokens();
                throw new ServiceError(
                    ServiceErrorType.UNKNOWN_ERROR,
                    'An unexpected error occurred while refreshing authentication.',
                    error
                );
            }
        } finally {
            // Always clear the promise when done
            tokenRefreshPromise = null;
        }
    })();

    return tokenRefreshPromise;
};

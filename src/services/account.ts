import type { AddAccountRequest, AddAccountResponse, AccountsResponse, AccountDetailResponse } from '../types/api';
import { createBaseApi, ServiceError, ServiceErrorType } from './baseService';

const accountAPI = createBaseApi();

export const getAccounts = async (cloudProviderId: number): Promise<AccountsResponse> => {
    const response = await accountAPI.get<AccountsResponse>('/api/accounts', {
        params: { cloud_provider_id: cloudProviderId }
    });
    return response.data;
};

export const addAccount = async (data: AddAccountRequest): Promise<AddAccountResponse> => {
    try {
        const response = await accountAPI.post<AddAccountResponse>('/api/accounts', data);
        return response.data;
    } catch (error) {
        if (error instanceof ServiceError) {
            if (error.type === ServiceErrorType.UNAUTHORIZED) {
                throw new Error('Invalid cloud provider credentials');
            }
            if (error.type === ServiceErrorType.CONFLICT_ERROR) {
                throw new Error('Account already exists');
            }
        }
        throw error;
    }
};

export const testConnection = async (data: AddAccountRequest): Promise<boolean> => {
    try {
        const response = await accountAPI.post<AddAccountResponse>('/api/accounts', data);
        return response.data.ok;
    } catch (error) {
        if (error instanceof ServiceError) {
            if (error.type === ServiceErrorType.UNAUTHORIZED) {
                throw new Error('Invalid cloud provider credentials');
            }
        }
        throw error;
    }
};

export const deleteAccount = async (accountId: number): Promise<void> => {
    await accountAPI.delete(`/api/accounts/${accountId}`);
};

/**
 * Get detailed information for a specific account
 * @param accountId The account ID
 * @returns Promise with the account detail response
 */
export const getAccountDetail = async (accountId: number): Promise<AccountDetailResponse> => {
    const response = await accountAPI.get<AccountDetailResponse>(`/api/accounts/${accountId}`);
    return response.data;
};
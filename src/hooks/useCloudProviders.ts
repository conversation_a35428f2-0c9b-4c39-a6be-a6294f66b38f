import { useQuery } from '@tanstack/react-query';
import { getCloudProviders, getServices } from '@services/dashboard';
import { CloudProvider } from '@/stores/cloudProviderStore';
import { QUERY_KEYS } from './useDashboard';
import { CloudProvider as ApiCloudProvider } from '@/types/api';
import { ServiceError } from '@/services/baseService';

/**
 * Custom hook to fetch cloud providers with their enabled/disabled states
 * @returns Cloud providers data and loading state
 */
export const useCloudProviders = () => {
    // Fetch cloud providers
    const {
        data,
        isLoading,
        error,
        refetch
    } = useQuery<ApiCloudProvider[], ServiceError, CloudProvider[]>({
        queryKey: [QUERY_KEYS.CLOUD_PROVIDERS],
        queryFn: async () => {
            try {
                const response = await getCloudProviders();
                return response.data.cloud_providers;
            } catch (error) {
                console.error('Failed to fetch cloud providers:', error);
                throw error;
            }
        },
        select: (providers) => {
            // Map API cloud providers to our internal CloudProvider type
            return providers.map(provider => ({
                id: provider.id,
                name: provider.name as CloudProvider['name'],
                is_enable: provider.is_enable
            }));
        },
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: (failureCount, error) => {
            // Don't retry on 4xx errors, but retry up to 3 times on other errors
            if (error.statusCode && error.statusCode >= 400 && error.statusCode < 500) {
                return false;
            }
            return failureCount < 3;
        }
    });

    return {
        cloudProviders: data || [],
        isLoading,
        error,
        refetch
    };
};

export const useServices = (cloudProviderId: number | null) => {
    return useQuery({
        queryKey: ['services', cloudProviderId],
        queryFn: () => {
            if (!cloudProviderId) throw new Error('Cloud provider ID is required');
            return getServices(cloudProviderId);
        },
        enabled: !!cloudProviderId,
        staleTime: 5 * 60 * 1000, // 5 minutes
    });
};
import React from "react";
import { useAuth } from "@/hooks/useAuth";
import Button from "@components/ui/Button";
import { RefreshCw } from "lucide-react";

interface TokenRefreshButtonProps {
  variant?:
    | "primary"
    | "secondary"
    | "accent"
    | "success"
    | "error"
    | "outline"
    | "ghost";
  size?: "sm" | "md" | "lg";
  className?: string;
}

/**
 * A button component that refreshes the authentication token when clicked
 */
const TokenRefreshButton: React.FC<TokenRefreshButtonProps> = ({
  variant = "outline",
  size = "sm",
  className = "",
}) => {
  const { refreshToken } = useAuth();

  const handleRefresh = () => {
    refreshToken.mutate();
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleRefresh}
      disabled={refreshToken.isPending}
      className={`flex items-center gap-2 ${className}`}
    >
      <RefreshCw
        size={16}
        className={refreshToken.isPending ? "animate-spin" : ""}
      />
      <span>Refresh Token</span>
    </Button>
  );
};

export default TokenRefreshButton;

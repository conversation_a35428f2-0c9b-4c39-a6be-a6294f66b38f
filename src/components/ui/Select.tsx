import React, { useState, useRef, useEffect } from "react";
import { Check, ChevronDown, Search, X } from "lucide-react";
import { cn } from "@/utils/cn";

export interface SelectOption {
  id: string | number;
  label: string;
  description?: string;
  icon?: React.ReactNode;
}

interface SelectProps {
  options: SelectOption[];
  selectedValue: string | number | null;
  onChange: (selectedValue: string | number) => void;
  placeholder?: string;
  className?: string;
  isLoading?: boolean;
  disabled?: boolean;
  error?: string;
  label?: string;
  required?: boolean;
}

const Select: React.FC<SelectProps> = ({
  options,
  selectedValue,
  onChange,
  placeholder = "Select an option...",
  className,
  isLoading = false,
  disabled = false,
  error,
  label,
  required = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Filter options based on search term
  const filteredOptions = options.filter(
    (option) =>
      option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (option.description &&
        option.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  const handleSelectOption = (optionId: string | number) => {
    onChange(optionId);
    setIsOpen(false);
    setSearchTerm("");
  };

  // Get the selected option
  const selectedOption = options.find((option) => option.id === selectedValue);

  return (
    <div className={cn("relative", className)}>
      {label && (
        <label className="mb-1 block font-medium text-dark-200 text-sm">
          {label} {required && <span className="text-error-500">*</span>}
        </label>
      )}
      <div ref={dropdownRef}>
        {/* Selected option display / dropdown trigger */}
        <div
          className={cn(
            "flex min-h-[42px] w-full items-center justify-between rounded-md border border-dark-700 bg-dark-900 px-3 py-2 text-dark-200",
            "hover:border-dark-600 focus:border-primary-500 focus:outline-none",
            isOpen && "border-primary-500",
            error && "border-error-500",
            disabled && "opacity-50 cursor-not-allowed",
            className
          )}
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          <div className="flex items-center gap-2 overflow-hidden">
            {selectedOption ? (
              <>
                {selectedOption.icon && (
                  <div className="flex-shrink-0">{selectedOption.icon}</div>
                )}
                <div className="truncate">
                  <span className="text-dark-200">{selectedOption.label}</span>
                  {selectedOption.description && (
                    <p className="text-xs text-dark-400">
                      {selectedOption.description}
                    </p>
                  )}
                </div>
              </>
            ) : (
              <span className="text-dark-400">{placeholder}</span>
            )}
          </div>
          <ChevronDown
            size={18}
            className={cn(
              "text-dark-400 transition-transform",
              isOpen && "rotate-180"
            )}
          />
        </div>

        {/* Dropdown */}
        {isOpen && (
          <div className="absolute z-10 mt-1 w-full rounded-md border border-dark-700 bg-dark-900 shadow-lg">
            {/* Search input */}
            <div className="flex items-center border-b border-dark-800 px-3 py-2">
              <Search size={16} className="text-dark-400" />
              <input
                ref={searchInputRef}
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search..."
                className="w-full bg-transparent px-2 py-1 text-dark-200 focus:outline-none"
              />
              {searchTerm && (
                <X
                  size={16}
                  className="cursor-pointer text-dark-400 hover:text-dark-200"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSearchTerm("");
                  }}
                />
              )}
            </div>

            {/* Options list */}
            <div className="max-h-60 overflow-y-auto p-1">
              {isLoading ? (
                <div className="flex items-center justify-center py-4">
                  <div className="h-5 w-5 animate-spin rounded-full border-2 border-dark-400 border-t-primary-500"></div>
                  <span className="ml-2 text-dark-400">Loading...</span>
                </div>
              ) : filteredOptions.length > 0 ? (
                filteredOptions.map((option) => (
                  <div
                    key={option.id}
                    className={cn(
                      "flex cursor-pointer items-center gap-2 rounded-md px-3 py-2 hover:bg-dark-800",
                      selectedValue === option.id && "bg-primary-500/10"
                    )}
                    onClick={() => handleSelectOption(option.id)}
                  >
                    {option.icon && <div className="flex-shrink-0">{option.icon}</div>}
                    <div className="flex-grow">
                      <div className="flex items-center justify-between">
                        <span className="text-dark-200">{option.label}</span>
                        {selectedValue === option.id && (
                          <Check size={16} className="text-primary-500" />
                        )}
                      </div>
                      {option.description && (
                        <div className="text-xs text-dark-400">
                          {option.description}
                        </div>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="py-3 text-center text-dark-400">
                  No results found
                </div>
              )}
            </div>
          </div>
        )}
      </div>
      {error && <p className="mt-1 text-sm text-error-500">{error}</p>}
    </div>
  );
};

export default Select;

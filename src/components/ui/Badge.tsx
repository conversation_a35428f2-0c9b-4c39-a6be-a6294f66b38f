import React from "react";
import { cn } from "@utils/cn";

type BadgeVariant =
  | "primary"
  | "secondary"
  | "accent"
  | "success"
  | "warning"
  | "error"
  | "neutral";
type BadgeSize = "sm" | "md" | "lg";

interface BadgeProps {
  variant?: BadgeVariant;
  size?: BadgeSize;
  children: React.ReactNode;
  className?: string;
}

const Badge: React.FC<BadgeProps> = ({
  variant = "primary",
  size = "md",
  children,
  className,
}) => {
  const variantClasses = {
    primary: "bg-primary-500/20 text-primary-400 border-primary-500/30",
    secondary: "bg-secondary-500/20 text-secondary-400 border-secondary-500/30",
    accent: "bg-accent-500/20 text-accent-400 border-accent-500/30",
    success: "bg-success-500/20 text-success-400 border-success-500/30",
    warning: "bg-warning-500/20 text-warning-400 border-warning-500/30",
    error: "bg-error-500/20 text-error-400 border-error-500/30",
    neutral: "bg-dark-700/40 text-dark-400 border-dark-600/30",
  };

  const sizeClasses = {
    sm: "text-xs px-2 py-0.5",
    md: "text-sm px-2.5 py-0.5",
    lg: "text-base px-3 py-1",
  };

  return (
    <span
      className={cn(
        "inline-flex items-center justify-center rounded-full font-medium border",
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
    >
      {children}
    </span>
  );
};

export default Badge;

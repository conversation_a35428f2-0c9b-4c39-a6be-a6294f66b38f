import React, { forwardRef } from "react";
import { cn } from "@utils/cn";

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      error,
      leftIcon,
      rightIcon,
      fullWidth = false,
      className,
      ...props
    },
    ref
  ) => {
    return (
      <div className={cn("flex flex-col", fullWidth ? "w-full" : "")}>
        {label && (
          <label
            className={cn(
              "mb-1 font-medium text-sm",
              props.disabled ? "text-dark-400" : "text-dark-200"
            )}
          >
            {label}
          </label>
        )}

        <div className="relative">
          {leftIcon && (
            <div
              className={cn(
                "absolute left-3 top-1/2 transform -translate-y-1/2",
                props.disabled ? "text-dark-500" : "text-dark-400"
              )}
            >
              {leftIcon}
            </div>
          )}

          <input
            ref={ref}
            className={cn(
              "bg-dark-800 border border-dark-700 rounded-md px-4 py-2 text-dark-100 placeholder:text-dark-500 focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500 transition-all",
              // Override browser autofill styles
              "[&:-webkit-autofill]:bg-dark-800 [&:-webkit-autofill]:bg-clip-text [&:-webkit-autofill]:text-dark-100 [&:-webkit-autofill]:shadow-[0_0_0_1000px_#1E293B_inset] [&:-webkit-autofill]:border-dark-700",
              // Maintain consistent background color on focus with autofill
              "[&:-webkit-autofill:focus]:shadow-[0_0_0_1000px_#1E293B_inset] [&:-webkit-autofill:focus]:border-primary-500",
              // Maintain consistent background color on hover with autofill
              "[&:-webkit-autofill:hover]:shadow-[0_0_0_1000px_#1E293B_inset]",
              leftIcon && "pl-10",
              rightIcon && "pr-10",
              error &&
                "border-error-500 focus:ring-error-500/50 focus:border-error-500",
              // Disabled state styling
              props.disabled &&
                "opacity-60 cursor-not-allowed bg-dark-900/50 text-dark-400 border-dark-800",
              fullWidth ? "w-full" : "",
              className
            )}
            {...props}
          />

          {rightIcon && (
            <div
              className={cn(
                "absolute right-3 top-1/2 transform -translate-y-1/2",
                props.disabled ? "text-dark-500" : "text-dark-400"
              )}
            >
              {rightIcon}
            </div>
          )}
        </div>

        {error && <p className="mt-1 text-sm text-error-500">{error}</p>}
      </div>
    );
  }
);

Input.displayName = "Input";

export default Input;

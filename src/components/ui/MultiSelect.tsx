import React, { useState, useRef, useEffect } from "react";
import { Check, ChevronDown, Search, X } from "lucide-react";
import { cn } from "@/utils/cn";

export interface Option {
  id: string | number;
  label: string;
  description?: string;
}

interface MultiSelectProps {
  options: Option[];
  selectedValues: (string | number)[];
  onChange: (selectedValues: (string | number)[]) => void;
  placeholder?: string;
  className?: string;
  isLoading?: boolean;
  disabled?: boolean;
}

const MultiSelect: React.FC<MultiSelectProps> = ({
  options,
  selectedValues,
  onChange,
  placeholder = "Select options...",
  className,
  isLoading = false,
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Filter options based on search term
  const filteredOptions = options.filter(
    (option) =>
      option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (option.description &&
        option.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Handle clicking outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        setIsOpen(false);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isOpen]);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  const toggleOption = (optionId: string | number) => {
    if (selectedValues.includes(optionId)) {
      onChange(selectedValues.filter((id) => id !== optionId));
    } else {
      onChange([...selectedValues, optionId]);
    }
  };

  const handleRemoveOption = (
    optionId: string | number,
    e: React.MouseEvent
  ) => {
    e.stopPropagation();
    onChange(selectedValues.filter((id) => id !== optionId));
  };

  return (
    <div className={cn("relative", className)} ref={dropdownRef}>
      {/* Selected options display / dropdown trigger */}
      <div
        className={cn(
          "flex min-h-[42px] w-full items-center justify-between rounded-md border border-dark-700 bg-dark-900 px-3 py-2 text-dark-200",
          "hover:border-dark-600 focus:border-primary-500 focus:outline-none",
          isOpen && "border-primary-500",
          disabled && "opacity-50 cursor-not-allowed",
          className
        )}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        <div className="flex flex-wrap gap-1.5">
          {selectedValues.length > 0 ? (
            selectedValues.map((value) => {
              const option = options.find((opt) => opt.id === value);
              return option ? (
                <div
                  key={option.id}
                  className="flex items-center gap-1 rounded-md bg-dark-800 px-2 py-1 text-xs"
                >
                  <span>{option.label}</span>
                  <X
                    size={14}
                    className="cursor-pointer text-dark-400 hover:text-dark-200"
                    onClick={(e) => handleRemoveOption(option.id, e)}
                  />
                </div>
              ) : null;
            })
          ) : (
            <span className="text-dark-400">{placeholder}</span>
          )}
        </div>
        <ChevronDown
          size={18}
          className={cn(
            "text-dark-400 transition-transform",
            isOpen && "rotate-180"
          )}
        />
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-10 mt-1 w-full rounded-md border border-dark-700 bg-dark-900 shadow-lg">
          {/* Search input */}
          <div className="flex items-center border-b border-dark-800 px-3 py-2">
            <Search size={16} className="text-dark-400" />
            <input
              ref={searchInputRef}
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search..."
              className="w-full bg-transparent px-2 py-1 text-dark-200 focus:outline-none"
            />
            {searchTerm && (
              <X
                size={16}
                className="cursor-pointer text-dark-400 hover:text-dark-200"
                onClick={() => setSearchTerm("")}
              />
            )}
          </div>

          {/* Options list */}
          <div className="max-h-60 overflow-y-auto p-1">
            {isLoading ? (
              <div className="flex items-center justify-center py-4">
                <div className="h-5 w-5 animate-spin rounded-full border-2 border-dark-400 border-t-primary-500"></div>
                <span className="ml-2 text-dark-400">Loading...</span>
              </div>
            ) : filteredOptions.length > 0 ? (
              <>
                {/* Select All option */}
                {filteredOptions.length > 1 && (
                  <div
                    className={cn(
                      "flex cursor-pointer items-start gap-2 rounded-md px-3 py-2 hover:bg-dark-800 border-b border-dark-800 mb-1",
                      filteredOptions.length ===
                        selectedValues.filter((id) =>
                          filteredOptions.some((opt) => opt.id === id)
                        ).length && "bg-primary-500/10"
                    )}
                    onClick={() => {
                      if (
                        filteredOptions.length ===
                        selectedValues.filter((id) =>
                          filteredOptions.some((opt) => opt.id === id)
                        ).length
                      ) {
                        // Deselect all filtered options
                        onChange(
                          selectedValues.filter(
                            (id) =>
                              !filteredOptions.some((opt) => opt.id === id)
                          )
                        );
                      } else {
                        // Select all filtered options
                        const filteredIds = filteredOptions.map(
                          (opt) => opt.id
                        );
                        const newSelected = [
                          ...new Set([...selectedValues, ...filteredIds]),
                        ];
                        onChange(newSelected);
                      }
                    }}
                  >
                    <div className="mt-0.5 flex h-4 w-4 items-center justify-center rounded border border-dark-600">
                      {filteredOptions.length ===
                        selectedValues.filter((id) =>
                          filteredOptions.some((opt) => opt.id === id)
                        ).length && (
                        <Check size={12} className="text-primary-500" />
                      )}
                    </div>
                    <div>
                      <div className="text-dark-200 font-medium">
                        {filteredOptions.length ===
                        selectedValues.filter((id) =>
                          filteredOptions.some((opt) => opt.id === id)
                        ).length
                          ? "Deselect All"
                          : "Select All"}
                      </div>
                    </div>
                  </div>
                )}

                {/* Individual options */}
                {filteredOptions.map((option) => (
                  <div
                    key={option.id}
                    className={cn(
                      "flex cursor-pointer items-start gap-2 rounded-md px-3 py-2 hover:bg-dark-800",
                      selectedValues.includes(option.id) && "bg-primary-500/10"
                    )}
                    onClick={() => toggleOption(option.id)}
                  >
                    <div className="mt-0.5 flex h-4 w-4 items-center justify-center rounded border border-dark-600">
                      {selectedValues.includes(option.id) && (
                        <Check size={12} className="text-primary-500" />
                      )}
                    </div>
                    <div>
                      <div className="text-dark-200">{option.label}</div>
                      {option.description && (
                        <div className="text-xs text-dark-400">
                          {option.description}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </>
            ) : (
              <div className="py-3 text-center text-dark-400">
                No results found
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default MultiSelect;

import { Toaster as HotToaster } from "react-hot-toast";

export const Toaster = () => {
  return (
    <HotToaster
      position="top-right"
      toastOptions={{
        className: "bg-dark-800 text-dark-100",
        duration: 4000,
        style: {
          background: "#1F2937",
          color: "#F3F4F6",
        },
        success: {
          iconTheme: {
            primary: "#10B981",
            secondary: "#F3F4F6",
          },
        },
        error: {
          iconTheme: {
            primary: "#EF4444",
            secondary: "#F3F4F6",
          },
        },
      }}
    />
  );
};

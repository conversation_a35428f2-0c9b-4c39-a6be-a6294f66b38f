import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@components/ui/Card";
import { Skeleton } from "@components/ui/Skeleton";
import { LoadingStateProps } from "./types";

/**
 * LoadingState component displays a skeleton UI while data is being loaded
 * 
 * @param message - Optional loading message to display
 */
const LoadingState: React.FC<LoadingStateProps> = ({ message = "Loading finding details..." }) => {
  return (
    <div className="space-y-8" aria-busy="true" aria-live="polite">
      {/* Visually hidden message for screen readers */}
      <div className="sr-only">{message}</div>
      
      {/* Header skeleton */}
      <div className="flex items-center gap-4">
        <Skeleton className="h-10 w-20" />
        <div>
          <Skeleton className="h-8 w-48 mb-2" />
          <Skeleton className="h-5 w-64" />
        </div>
      </div>

      {/* Summary card skeleton */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <Skeleton className="h-5 w-24 mb-2" />
              <Skeleton className="h-6 w-32" />
            </div>
            <div>
              <Skeleton className="h-5 w-24 mb-2" />
              <Skeleton className="h-6 w-32" />
            </div>
            <div>
              <Skeleton className="h-5 w-24 mb-2" />
              <Skeleton className="h-6 w-32" />
            </div>
            <div>
              <Skeleton className="h-5 w-24 mb-2" />
              <Skeleton className="h-6 w-32" />
            </div>
            
            {/* Description skeleton */}
            <div className="col-span-full">
              <Skeleton className="h-24 w-full rounded-lg" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Details card skeleton */}
      <Card>
        <CardHeader>
          <Skeleton className="h-7 w-36 mb-2" />
          <Skeleton className="h-5 w-64" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-64 w-full" />
        </CardContent>
      </Card>
    </div>
  );
};

export default LoadingState;

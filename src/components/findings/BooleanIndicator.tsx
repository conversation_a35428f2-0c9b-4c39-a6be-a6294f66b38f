import React from "react";
import { CheckCircle, XCircle } from "lucide-react";
import { BooleanIndicatorProps } from "./types";
import { cn } from "@/utils/cn";

/**
 * BooleanIndicator component displays a visual indicator for boolean values
 * 
 * @param value - The boolean value to display
 * @param size - The size of the indicator (sm, md, lg)
 */
const BooleanIndicator: React.FC<BooleanIndicatorProps> = ({ 
  value,
  size = "md" 
}) => {
  // Determine the appropriate classes based on value and size
  const containerClasses = cn(
    "rounded-full flex items-center justify-center",
    {
      "bg-success-500/20 text-success-500": value,
      "bg-error-500/20 text-error-500": !value,
      "w-5 h-5": size === "sm",
      "w-6 h-6": size === "md",
      "w-8 h-8": size === "lg",
    }
  );

  // Determine the icon size based on indicator size
  const iconSize = size === "sm" ? 12 : size === "md" ? 14 : 16;

  return (
    <div 
      className={containerClasses} 
      role="status" 
      aria-label={value ? "True" : "False"}
    >
      {value ? (
        <CheckCircle size={iconSize} aria-hidden="true" />
      ) : (
        <XCircle size={iconSize} aria-hidden="true" />
      )}
    </div>
  );
};

export default BooleanIndicator;

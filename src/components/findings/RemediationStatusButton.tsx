import React from "react";
import Button from "@components/ui/Button";
import { Check<PERSON><PERSON><PERSON>, XCircle, Loader2, RefreshCw } from "lucide-react";
import { RemediationStatusButtonProps } from "./types";
import { cn } from "@/utils/cn";

/**
 * RemediationStatusButton component displays a button showing the status of a remediation
 * and allows viewing the details
 */
const RemediationStatusButton: React.FC<RemediationStatusButtonProps> = ({
  remediate,
  onClick,
}) => {
  // Determine the status of the remediation
  const status = remediate.status.toLowerCase();
  const isSuccess = status === "pass";
  const isRemediated = status === "remediated";
  const isFailed = status === "failed" || status === "fail";
  const isPending = !isSuccess && !isFailed && !isRemediated;

  // Determine the appropriate button text based on status
  const buttonText = isSuccess
    ? "View Success"
    : isRemediated
    ? "View Remediated"
    : isFailed
    ? "View Error"
    : "View Status";

  // Determine the appropriate button classes based on status
  const buttonClasses = cn({
    "border-success-500/50 text-success-500 hover:bg-success-500/10 hover:border-success-500":
      isSuccess,
    "border-primary-500/50 text-primary-500 hover:bg-primary-500/10 hover:border-primary-500":
      isRemediated,
    "border-error-500/50 text-error-500 hover:bg-error-500/10 hover:border-error-500":
      isFailed,
    "border-warning-500/50 text-warning-500 hover:bg-warning-500/10 hover:border-warning-500":
      isPending,
  });

  // Determine the appropriate icon based on status
  const icon = isSuccess ? (
    <CheckCircle size={14} />
  ) : isRemediated ? (
    <RefreshCw size={14} />
  ) : isFailed ? (
    <XCircle size={14} />
  ) : (
    <Loader2 size={14} className="animate-spin" />
  );

  return (
    <div className="ml-4">
      <Button
        variant="outline"
        size="sm"
        className={buttonClasses}
        leftIcon={icon}
        onClick={onClick}
        aria-label={`View remediation ${
          isSuccess
            ? "success"
            : isRemediated
            ? "remediated"
            : isFailed
            ? "error"
            : "status"
        } details`}
      >
        {buttonText}
      </Button>
    </div>
  );
};

export default React.memo(RemediationStatusButton);

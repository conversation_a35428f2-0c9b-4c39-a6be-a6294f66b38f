import { formatDate } from "@/lib/dateUtils";
import Button from "@components/ui/Button";
import Modal from "@components/ui/Modal";
import {
  AlertCircle,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Loader2,
  RefreshCw,
  ShieldAlert,
  User,
  XCircle,
} from "lucide-react";
import React, { useState } from "react";
import {
  RemediationDetailsModalProps,
  getRemediationMessage,
  getRemediationStatus,
} from "./types";

/**
 * RemediationDetailsModal component displays detailed information about a remediation attempt
 * including status, user, timestamp, and message with proper error handling
 */
const RemediationDetailsModal: React.FC<RemediationDetailsModalProps> = ({
  isOpen,
  onClose,
  detail,
  detailIndex,
  findingId,
  onRemediate,
  isRemediating,
}) => {
  // State for expandable error message
  const [isExpanded, setIsExpanded] = useState(false);

  // Get remediation status and message
  const remediateStatus = getRemediationStatus(detail);
  const message = getRemediationMessage(detail);

  // Determine if message is long enough to truncate
  const isLongMessage = message.length > 100;

  // Handle retry button click
  const handleRetry = React.useCallback(() => {
    onRemediate(findingId, detailIndex, detail);
  }, [findingId, detailIndex, detail, onRemediate]);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Remediation Details"
      size="md"
      aria-labelledby="remediation-modal-title"
    >
      <div className="p-6">
        {/* Status Header */}
        <div
          className={`px-4 py-3 rounded-lg flex items-center justify-between mb-4 ${
            remediateStatus === "pass"
              ? "bg-success-500/10 border border-success-500/30"
              : remediateStatus === "remediated"
              ? "bg-primary-500/10 border border-primary-500/30"
              : remediateStatus === "failed" || remediateStatus === "fail"
              ? "bg-error-500/10 border border-error-500/30"
              : "bg-warning-500/10 border border-warning-500/30"
          }`}
          role="status"
          aria-live="polite"
        >
          <div className="flex items-center">
            <div
              className={`p-2 rounded-full mr-3 ${
                remediateStatus === "pass"
                  ? "bg-success-500/20"
                  : remediateStatus === "remediated"
                  ? "bg-primary-500/20"
                  : remediateStatus === "failed" || remediateStatus === "fail"
                  ? "bg-error-500/20"
                  : "bg-warning-500/20"
              }`}
              aria-hidden="true"
            >
              {remediateStatus === "pass" ? (
                <CheckCircle size={18} className="text-success-500" />
              ) : remediateStatus === "remediated" ? (
                <RefreshCw size={18} className="text-primary-500" />
              ) : remediateStatus === "failed" || remediateStatus === "fail" ? (
                <XCircle size={18} className="text-error-500" />
              ) : (
                <Loader2 size={18} className="text-warning-500 animate-spin" />
              )}
            </div>
            <div>
              <div className="font-medium" id="remediation-modal-title">
                {remediateStatus === "pass"
                  ? "Remediation Successful"
                  : remediateStatus === "remediated"
                  ? "Finding Remediated"
                  : remediateStatus === "failed" || remediateStatus === "fail"
                  ? "Remediation Failed"
                  : "Remediation Pending"}
              </div>
              {detail.remediate?.attempted_at && (
                <div className="text-xs text-dark-400">
                  {formatDate(detail.remediate.attempted_at, {
                    includeTime: true,
                  })}
                </div>
              )}
            </div>
          </div>

          {/* Status Badge */}
          <div
            className={`px-2 py-1 rounded-full text-xs font-medium ${
              remediateStatus === "pass"
                ? "bg-success-500/20 text-success-500"
                : remediateStatus === "remediated"
                ? "bg-primary-500/20 text-primary-500"
                : remediateStatus === "failed" || remediateStatus === "fail"
                ? "bg-error-500/20 text-error-500"
                : "bg-warning-500/20 text-warning-500"
            }`}
          >
            {remediateStatus.toUpperCase()}
          </div>
        </div>

        {/* User Info */}
        {detail.remediate?.attempted_by && (
          <div className="flex items-center mb-4 text-sm text-dark-300 bg-dark-850 p-3 rounded-lg">
            <User size={16} className="mr-2" aria-hidden="true" />
            <span>
              Attempted by:{" "}
              {typeof detail.remediate.attempted_by === "object" &&
              detail.remediate.attempted_by !== null
                ? ((attemptedBy) => {
                    // Type assertion to help TypeScript understand the structure
                    const user = attemptedBy as {
                      id: number;
                      email?: string;
                    };
                    return user.email || `User #${user.id}`;
                  })(detail.remediate.attempted_by)
                : `User #${detail.remediate.attempted_by}`}
            </span>
          </div>
        )}

        {/* Message Content */}
        <div className="mb-6">
          <h3
            className="text-dark-300 text-sm mb-2"
            id="remediation-message-heading"
          >
            Message:
          </h3>
          <div
            className={`p-4 rounded-lg ${
              remediateStatus === "pass" || remediateStatus === "remediated"
                ? "bg-dark-850 text-dark-200"
                : remediateStatus === "failed" || remediateStatus === "fail"
                ? "bg-error-500/10 border border-error-500/30 text-error-400"
                : "bg-dark-850 text-dark-200"
            }`}
            aria-labelledby="remediation-message-heading"
          >
            <div className="flex items-start">
              {(remediateStatus === "failed" || remediateStatus === "fail") && (
                <AlertCircle
                  size={18}
                  className="mr-2 mt-0.5 flex-shrink-0"
                  aria-hidden="true"
                />
              )}
              <div className="flex-1">
                {/* Message container with improved overflow handling */}
                <div
                  className={`
                    ${isLongMessage && !isExpanded ? "max-h-24" : "max-h-60"}
                    ${isLongMessage ? "overflow-y-auto" : ""}
                    break-all break-words whitespace-pre-line
                    pr-1 scrollbar-thin scrollbar-thumb-dark-600 scrollbar-track-dark-800
                  `}
                  tabIndex={0}
                >
                  {message}
                </div>

                {/* Show expand/collapse button for long messages */}
                {isLongMessage && (
                  <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className={`text-xs mt-2 flex items-center ${
                      remediateStatus === "failed" || remediateStatus === "fail"
                        ? "text-error-400 hover:text-error-300"
                        : "text-dark-400 hover:text-dark-300"
                    }`}
                    aria-expanded={isExpanded}
                    aria-controls="remediation-message"
                  >
                    {isExpanded ? (
                      <>
                        <ChevronUp
                          size={12}
                          className="mr-1"
                          aria-hidden="true"
                        />
                        Show less
                      </>
                    ) : (
                      <>
                        <ChevronDown
                          size={12}
                          className="mr-1"
                          aria-hidden="true"
                        />
                        Show more
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose} aria-label="Close dialog">
            Close
          </Button>

          {/* Show Retry Button for Failed Remediations */}
          {(remediateStatus === "failed" || remediateStatus === "fail") && (
            <Button
              variant="primary"
              className="bg-error-500 hover:bg-error-600 border-error-500"
              leftIcon={<ShieldAlert size={16} />}
              onClick={handleRetry}
              isLoading={isRemediating}
              disabled={isRemediating}
              aria-label="Try remediation again"
            >
              {isRemediating ? "Retrying..." : "Try Again"}
            </Button>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default React.memo(RemediationDetailsModal);

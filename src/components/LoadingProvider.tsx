import React, { createContext, useContext, useState, useEffect } from "react";
import LoadingScreen from "./LoadingScreen";

interface LoadingContextType {
  /**
   * Start a global loading state with an optional message and progress
   */
  startLoading: (message?: string, progress?: number) => void;
  
  /**
   * Update the loading message and/or progress
   */
  updateLoading: (message?: string, progress?: number) => void;
  
  /**
   * End the global loading state
   */
  endLoading: () => void;
  
  /**
   * Current loading state
   */
  isLoading: boolean;
  
  /**
   * Current loading message
   */
  message: string;
  
  /**
   * Current loading progress (0-100)
   */
  progress?: number;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

/**
 * Provider component for managing global loading states
 */
export const LoadingProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState("Loading...");
  const [progress, setProgress] = useState<number | undefined>(undefined);
  const [loadingTimeout, setLoadingTimeout] = useState<NodeJS.Timeout | null>(null);

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (loadingTimeout) {
        clearTimeout(loadingTimeout);
      }
    };
  }, [loadingTimeout]);

  const startLoading = (newMessage?: string, newProgress?: number) => {
    // Clear any existing timeout
    if (loadingTimeout) {
      clearTimeout(loadingTimeout);
      setLoadingTimeout(null);
    }
    
    setIsLoading(true);
    if (newMessage) setMessage(newMessage);
    if (newProgress !== undefined) setProgress(newProgress);
    
    // Set a safety timeout to prevent infinite loading states
    const timeout = setTimeout(() => {
      console.warn("Loading state timed out after 30 seconds");
      setIsLoading(false);
    }, 30000);
    
    setLoadingTimeout(timeout);
  };

  const updateLoading = (newMessage?: string, newProgress?: number) => {
    if (newMessage) setMessage(newMessage);
    if (newProgress !== undefined) setProgress(newProgress);
  };

  const endLoading = () => {
    // Clear any existing timeout
    if (loadingTimeout) {
      clearTimeout(loadingTimeout);
      setLoadingTimeout(null);
    }
    
    setIsLoading(false);
    setProgress(undefined);
  };

  return (
    <LoadingContext.Provider
      value={{
        startLoading,
        updateLoading,
        endLoading,
        isLoading,
        message,
        progress,
      }}
    >
      {isLoading && <LoadingScreen message={message} progress={progress} />}
      {children}
    </LoadingContext.Provider>
  );
};

/**
 * Hook to use the loading context
 */
export const useLoading = (): LoadingContextType => {
  const context = useContext(LoadingContext);
  if (context === undefined) {
    throw new Error("useLoading must be used within a LoadingProvider");
  }
  return context;
};

export default LoadingProvider;

import React, { ReactNode } from "react";
import { usePermissionContext } from "@/contexts/PermissionContext";
import { PermissionName } from "@/types/permissions";

/**
 * Props for the PermissionGuard component
 */
interface PermissionGuardProps {
  /**
   * The permission(s) required to render the children
   * If an array is provided, the user must have ALL permissions to render the children
   */
  permissions: PermissionName | PermissionName[];

  /**
   * If true, the user must have ANY of the permissions to render the children
   * If false (default), the user must have ALL permissions
   */
  anyPermission?: boolean;

  /**
   * The children to render if the user has the required permissions
   */
  children: ReactNode;

  /**
   * Optional fallback to render if the user doesn't have the required permissions
   */
  fallback?: ReactNode;
}

/**
 * Component that conditionally renders its children based on user permissions
 */
const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permissions,
  anyPermission = false,
  children,
  fallback = null,
}) => {
  const { hasAllPermissions, hasAnyPermission } = usePermissionContext();

  // Convert single permission to array
  const permissionArray = Array.isArray(permissions)
    ? permissions
    : [permissions];

  // Check if the user has the required permissions
  const hasRequiredPermissions = anyPermission
    ? hasAnyPermission(permissionArray)
    : hasAllPermissions(permissionArray);

  // Render children if the user has the required permissions, otherwise render fallback
  return hasRequiredPermissions ? <>{children}</> : <>{fallback}</>;
};

export default PermissionGuard;

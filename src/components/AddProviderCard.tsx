import React from "react";
import { Plus } from "lucide-react";
import { motion } from "framer-motion";
import { Card, CardContent } from "./ui/Card";
import { CloudProviderName } from "../stores/cloudProviderStore";

interface AddProviderCardProps {
  provider: CloudProviderName;
  isEnabled?: boolean;
  onClick: () => void;
}

const getProviderGradient = (provider: CloudProviderName) => {
  switch (provider) {
    case "AWS":
      return "from-accent-500/20 to-accent-600/5";
    case "GCP":
      return "from-primary-500/20 to-primary-600/5";
    case "Azure":
      return "from-secondary-500/20 to-secondary-600/5";
    default:
      return "from-dark-700/20 to-dark-800/5";
  }
};

const getProviderBorder = (provider: CloudProviderName) => {
  switch (provider) {
    case "AWS":
      return "border-accent-500/30 hover:border-accent-500/70";
    case "GCP":
      return "border-primary-500/30 hover:border-primary-500/70";
    case "Azure":
      return "border-secondary-500/30 hover:border-secondary-500/70";
    default:
      return "border-dark-600/30 hover:border-dark-500/50";
  }
};

const AddProviderCard: React.FC<AddProviderCardProps> = ({
  provider,
  isEnabled = true,
  onClick,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card
        className={`h-full ${
          isEnabled ? "cursor-pointer" : "cursor-not-allowed opacity-60"
        } bg-gradient-to-br ${getProviderGradient(
          provider
        )} ${getProviderBorder(provider)} ${
          isEnabled ? "hover:shadow-lg" : ""
        } transition-all duration-300`}
        onClick={isEnabled ? onClick : undefined}
      >
        <CardContent className="flex flex-col items-center justify-center h-full p-8 text-center">
          <div className="mb-4 p-3 rounded-full bg-dark-800/50 border border-dark-700/50">
            <Plus size={24} className="text-dark-200" />
          </div>
          <h3 className="text-lg font-semibold mb-2 text-dark-200">
            Add {provider}
          </h3>
          <p className="text-sm text-dark-400">
            {isEnabled
              ? `Connect your ${provider} account to scan for vulnerabilities`
              : `${provider} integration is currently disabled`}
          </p>
          {!isEnabled && (
            <div className="mt-3 px-3 py-1 bg-dark-800/70 rounded-md text-xs text-dark-300">
              Coming soon
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default AddProviderCard;

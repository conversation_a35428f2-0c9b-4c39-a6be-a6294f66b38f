# Docker Compose configuration for CloudAudit Frontend - Development Environment
version: '3.8'

services:
  # Frontend Application - Development
  cloudaudit-frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        NODE_VERSION: 20
    container_name: cloudaudit-frontend-dev
    restart: unless-stopped
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=development
      - VITE_BACKEND_LOCAL_BASE_URL=http://localhost:8000/api
      - VITE_APP_ENV=development
      - VITE_ENABLE_DEVTOOLS=true
      - VITE_ENABLE_DEBUG_LOGGING=true
      - VITE_ENABLE_HMR=true
    volumes:
      # Mount source code for hot reloading
      - .:/app
      - /app/node_modules
      - /app/dist
    networks:
      - cloudaudit-dev-network
    command: npm run dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  cloudaudit-dev-network:
    driver: bridge
    name: cloudaudit-dev-network

# Multi-stage Docker build for CloudAudit Frontend
# Stage 1: Build the application
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    git \
    python3 \
    make \
    g++

# Copy package files
COPY package*.json ./

# Install dependencies with clean install for production
RUN npm ci --only=production --silent

# Copy source code
COPY . .

# Build arguments for environment configuration
ARG BUILD_MODE=production
ARG VITE_BACKEND_LOCAL_BASE_URL
ARG VITE_APP_ENV
ARG VITE_APP_NAME
ARG VITE_APP_BASE_URL
ARG VITE_SENTRY_DSN
ARG VITE_GA_ID
ARG VITE_CDN_BASE_URL

# Set environment variables for build
ENV NODE_ENV=production
ENV VITE_APP_ENV=${VITE_APP_ENV}
ENV VITE_BACKEND_LOCAL_BASE_URL=${VITE_BACKEND_LOCAL_BASE_URL}
ENV VITE_APP_NAME=${VITE_APP_NAME}
ENV VITE_APP_BASE_URL=${VITE_APP_BASE_URL}
ENV VITE_SENTRY_DSN=${VITE_SENTRY_DSN}
ENV VITE_GA_ID=${VITE_GA_ID}
ENV VITE_CDN_BASE_URL=${VITE_CDN_BASE_URL}

# Build the application
RUN npm run build:${BUILD_MODE}

# Stage 2: Production server with Nginx
FROM nginx:alpine AS production

# Install additional packages for better security and functionality
RUN apk add --no-cache \
    curl \
    tzdata \
    && rm -rf /var/cache/apk/*

# Create nginx user and group
RUN addgroup -g 1001 -S nginx-app && \
    adduser -S -D -H -u 1001 -h /var/cache/nginx -s /sbin/nologin -G nginx-app -g nginx-app nginx-app

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf
COPY nginx-default.conf /etc/nginx/conf.d/default.conf

# Copy built application from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy health check script
COPY docker-healthcheck.sh /usr/local/bin/healthcheck.sh
RUN chmod +x /usr/local/bin/healthcheck.sh

# Set proper permissions
RUN chown -R nginx-app:nginx-app /usr/share/nginx/html && \
    chown -R nginx-app:nginx-app /var/cache/nginx && \
    chown -R nginx-app:nginx-app /var/log/nginx && \
    chown -R nginx-app:nginx-app /etc/nginx/conf.d

# Create directories for nginx
RUN mkdir -p /var/cache/nginx/client_temp && \
    mkdir -p /var/cache/nginx/proxy_temp && \
    mkdir -p /var/cache/nginx/fastcgi_temp && \
    mkdir -p /var/cache/nginx/uwsgi_temp && \
    mkdir -p /var/cache/nginx/scgi_temp && \
    chown -R nginx-app:nginx-app /var/cache/nginx

# Switch to non-root user
USER nginx-app

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD /usr/local/bin/healthcheck.sh

# Labels for better container management
LABEL maintainer="CloudAudit Team"
LABEL version="1.0"
LABEL description="CloudAudit Frontend Application"

# Start nginx
CMD ["nginx", "-g", "daemon off;"]

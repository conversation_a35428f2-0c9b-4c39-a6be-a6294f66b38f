import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current working directory.
  const env = loadEnv(mode, process.cwd(), '');

  const isProduction = mode === 'production';

  return {
    plugins: [react()],

    // Server configuration
    server: {
      host: env.VITE_DEV_HOST || '0.0.0.0',
      port: parseInt(env.VITE_DEV_PORT) || 5173,
      hmr: env.VITE_ENABLE_HMR !== 'false',
    },

    // Preview server configuration
    preview: {
      host: '0.0.0.0',
      port: 4173,
    },

    // Build configuration
    build: {
      outDir: env.VITE_BUILD_OUTPUT_DIR || 'dist',
      sourcemap: env.VITE_ENABLE_SOURCE_MAPS === 'true' || !isProduction,
      minify: isProduction ? 'esbuild' : false,
      target: 'es2020',

      // Chunk splitting for better caching
      rollupOptions: {
        output: {
          manualChunks: {
            // Vendor chunks
            'react-vendor': ['react', 'react-dom'],
            'router-vendor': ['react-router-dom'],
            'query-vendor': ['@tanstack/react-query'],
            'ui-vendor': ['framer-motion', 'lucide-react'],
            'form-vendor': ['react-hook-form', '@hookform/resolvers', 'zod'],
          },
          // Asset naming for better caching
          chunkFileNames: isProduction
            ? 'assets/js/[name]-[hash].js'
            : 'assets/js/[name].js',
          entryFileNames: isProduction
            ? 'assets/js/[name]-[hash].js'
            : 'assets/js/[name].js',
          assetFileNames: isProduction
            ? 'assets/[ext]/[name]-[hash].[ext]'
            : 'assets/[ext]/[name].[ext]',
        },
      },

      // Compression and optimization
      cssCodeSplit: true,
      assetsInlineLimit: 4096, // 4kb

      // Performance optimizations for production
      ...(isProduction && {
        reportCompressedSize: false,
        chunkSizeWarningLimit: 1000,
      }),
    },

    // Dependency optimization
    optimizeDeps: {
      exclude: ['lucide-react'],
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@tanstack/react-query',
        'axios',
        'framer-motion',
      ],
    },

    // Path resolution
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
        '@components': resolve(__dirname, './src/components'),
        '@utils': resolve(__dirname, './src/utils'),
        '@hooks': resolve(__dirname, './src/hooks'),
        '@assets': resolve(__dirname, './src/assets'),
        '@stores': resolve(__dirname, './src/stores'),
        '@pages': resolve(__dirname, './src/pages'),
        '@types': resolve(__dirname, './src/types'),
        '@services': resolve(__dirname, './src/services'),
        '@lib': resolve(__dirname, './src/lib'),
        '@schemas': resolve(__dirname, './src/schemas'),
        '@contexts': resolve(__dirname, './src/contexts'),
        '@layouts': resolve(__dirname, './src/layouts'),
      },
    },

    // Environment variables
    define: {
      __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
      __BUILD_MODE__: JSON.stringify(mode),
    },

    // CSS configuration
    css: {
      devSourcemap: !isProduction,
      postcss: './postcss.config.js',
    },

    // Enable bundle analysis in staging/development
    ...(env.VITE_ENABLE_BUNDLE_ANALYSIS === 'true' && {
      plugins: [
        react(),
        // Add bundle analyzer plugin if needed
      ],
    }),
  };
});

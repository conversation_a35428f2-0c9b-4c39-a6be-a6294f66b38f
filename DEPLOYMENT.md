# CloudAudit Frontend - Production Deployment Guide

This guide provides comprehensive instructions for deploying the CloudAudit Frontend application to production environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Configuration](#environment-configuration)
3. [Docker Deployment](#docker-deployment)
4. [Manual Deployment](#manual-deployment)
5. [CI/CD Setup](#cicd-setup)
6. [Monitoring and Maintenance](#monitoring-and-maintenance)
7. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

- **Operating System**: Linux (Ubuntu 20.04+ recommended), macOS, or Windows with WSL2
- **Docker**: Version 20.10+
- **Docker Compose**: Version 2.0+
- **Node.js**: Version 20+ (for manual deployment)
- **Git**: Latest version
- **Minimum Hardware**:
  - CPU: 2 cores
  - RAM: 4GB
  - Storage: 20GB free space
  - Network: Stable internet connection

### Domain and SSL

- Domain name configured to point to your server
- SSL certificate (Let's Encrypt recommended)
- Firewall configured to allow HTTP (80) and HTTPS (443) traffic

## Environment Configuration

### 1. Environment Files

Copy the appropriate environment template:

```bash
# For production
cp .env.example .env.production

# For staging
cp .env.example .env.staging
```

### 2. Required Environment Variables

Edit your environment file with the following required variables:

```bash
# API Configuration
VITE_BACKEND_LOCAL_BASE_URL=https://api.cloudaudit.com/api
VITE_APP_BASE_URL=https://app.cloudaudit.com

# Application Settings
VITE_APP_ENV=production
VITE_APP_NAME=CloudAudit

# Security (Optional but recommended)
VITE_SENTRY_DSN=your-sentry-dsn
VITE_GA_ID=your-google-analytics-id

# CDN (Optional)
VITE_CDN_BASE_URL=https://cdn.cloudaudit.com
```

## Docker Deployment (Recommended)

### 1. Quick Start

```bash
# Clone the repository
git clone https://github.com/your-org/cloudaudit-frontend.git
cd cloudaudit-frontend

# Set up environment
cp .env.example .env.production
# Edit .env.production with your configuration

# Deploy to production
./deploy.sh production
```

### 2. Manual Docker Deployment

```bash
# Build the Docker image
docker-compose build

# Start the application
docker-compose up -d

# Check status
docker-compose ps
docker-compose logs -f
```

### 3. Staging Deployment

```bash
# Deploy to staging
./deploy.sh staging

# Or manually
docker-compose -f docker-compose.staging.yml up -d
```

### 4. Production Setup Script

For a complete production setup on a new server:

```bash
# Make scripts executable (Linux/macOS)
chmod +x scripts/setup-production.sh

# Run the setup script
./scripts/setup-production.sh
```

## Manual Deployment

### 1. Build the Application

```bash
# Install dependencies
npm ci

# Build for production
npm run build:production

# Or for staging
npm run build:staging
```

### 2. Serve with Nginx

```bash
# Copy built files to web server directory
sudo cp -r dist/* /var/www/html/

# Configure Nginx (see nginx-default.conf for reference)
sudo cp nginx-default.conf /etc/nginx/sites-available/cloudaudit-frontend
sudo ln -s /etc/nginx/sites-available/cloudaudit-frontend /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## CI/CD Setup

### 1. GitHub Actions

The repository includes a GitHub Actions workflow (`.github/workflows/deploy.yml`) that automatically:

- Runs tests and linting
- Builds Docker images
- Deploys to staging/production based on branch

### 2. Required Secrets

Configure the following secrets in your GitHub repository:

```
# Staging Environment
STAGING_HOST=your-staging-server-ip
STAGING_USER=deployment-user
STAGING_SSH_KEY=your-private-ssh-key

# Production Environment
PRODUCTION_HOST=your-production-server-ip
PRODUCTION_USER=deployment-user
PRODUCTION_SSH_KEY=your-private-ssh-key

# Optional: Notifications
SLACK_WEBHOOK_URL=your-slack-webhook-url
```

### 3. Deployment Workflow

- **Push to `develop`**: Deploys to staging
- **Push to `staging`**: Deploys to staging
- **Push to `main`**: Deploys to production

## Monitoring and Maintenance

### 1. Health Monitoring

```bash
# Check application health
curl http://localhost/health

# Monitor with the provided script
./scripts/monitor.sh app.cloudaudit.com --report
```

### 2. Log Management

```bash
# View application logs
docker-compose logs -f

# View Nginx logs
docker-compose exec cloudaudit-frontend tail -f /var/log/nginx/access.log
docker-compose exec cloudaudit-frontend tail -f /var/log/nginx/error.log
```

### 3. Backup

```bash
# Manual backup
./scripts/backup.sh

# Automated backups are configured via cron in the setup script
```

### 4. Updates

```bash
# Update application
git pull origin main
docker-compose build
docker-compose up -d

# Or use the deployment script
./deploy.sh production --no-cache
```

## SSL/TLS Configuration

### 1. Let's Encrypt with Certbot

```bash
# Install Certbot
sudo apt-get update
sudo apt-get install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d app.cloudaudit.com

# Auto-renewal (already configured in setup script)
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 2. Custom SSL Certificate

If using a custom SSL certificate, update the Nginx configuration:

```nginx
server {
    listen 443 ssl http2;
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    # ... rest of configuration
}
```

## Performance Optimization

### 1. CDN Configuration

Configure a CDN for static assets by setting:

```bash
VITE_CDN_BASE_URL=https://cdn.cloudaudit.com
```

### 2. Caching

The Nginx configuration includes optimized caching headers for static assets.

### 3. Compression

Gzip compression is enabled by default in the Nginx configuration.

## Security Considerations

### 1. Environment Variables

- Never commit `.env.production` to version control
- Use secrets management for sensitive data
- Regularly rotate API keys and tokens

### 2. Network Security

- Configure firewall to only allow necessary ports
- Use HTTPS everywhere
- Implement rate limiting (configured in Nginx)

### 3. Container Security

- Containers run as non-root user
- Minimal base images used
- Regular security updates

## Troubleshooting

### Common Issues

1. **Application not starting**
   ```bash
   # Check logs
   docker-compose logs cloudaudit-frontend
   
   # Check container status
   docker-compose ps
   ```

2. **502 Bad Gateway**
   ```bash
   # Check if container is running
   docker-compose ps
   
   # Check Nginx configuration
   docker-compose exec cloudaudit-frontend nginx -t
   ```

3. **Environment variables not loading**
   ```bash
   # Verify environment file exists and has correct values
   cat .env.production
   
   # Rebuild with no cache
   docker-compose build --no-cache
   ```

4. **SSL certificate issues**
   ```bash
   # Check certificate status
   sudo certbot certificates
   
   # Renew certificate
   sudo certbot renew
   ```

### Getting Help

- Check the application logs first
- Review the monitoring script output
- Consult the GitHub repository issues
- Contact the development team

## Maintenance Schedule

### Daily
- Monitor application health
- Check error logs
- Verify backup completion

### Weekly
- Review performance metrics
- Update dependencies (staging first)
- Security scan

### Monthly
- SSL certificate renewal check
- Full system backup
- Performance optimization review

---

For additional support or questions, please refer to the project documentation or contact the development team.

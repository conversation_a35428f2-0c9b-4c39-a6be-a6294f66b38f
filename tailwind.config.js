/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#3B82F6',
          50: '#EBF2FF',
          100: '#D7E5FD',
          200: '#B0CBFC',
          300: '#88B1FA',
          400: '#6197F9',
          500: '#3B82F6',
          600: '#0B60EA',
          700: '#0747B3',
          800: '#052F77',
          900: '#03193C',
        },
        secondary: {
          DEFAULT: '#14B8A6',
          50: '#E0F7F5',
          100: '#C2F0EB',
          200: '#86E5DC',
          300: '#4BD8CA',
          400: '#1ECFBE',
          500: '#14B8A6',
          600: '#0F8C7E',
          700: '#0A5F55',
          800: '#05322D',
          900: '#021A18',
        },
        accent: {
          DEFAULT: '#F97316',
          50: '#FEF0E6',
          100: '#FDE0CD',
          200: '#FCC29A',
          300: '#FBA368',
          400: '#FA8535',
          500: '#F97316',
          600: '#D45A05',
          700: '#A14404',
          800: '#6F2F03',
          900: '#3D1A01',
        },
        success: {
          DEFAULT: '#22C55E',
          50: '#E8F8EE',
          100: '#D1F1DE',
          200: '#A3E4BD',
          300: '#75D69C',
          400: '#47C97B',
          500: '#22C55E',
          600: '#1A9947',
          700: '#136C33',
          800: '#0B3E1D',
          900: '#041F0F',
        },
        warning: {
          DEFAULT: '#F59E0B',
          50: '#FEF5E7',
          100: '#FDEBD0',
          200: '#FCD8A1',
          300: '#FAC471',
          400: '#F7AF3E',
          500: '#F59E0B',
          600: '#C47F08',
          700: '#925F06',
          800: '#613F04',
          900: '#312002',
        },
        error: {
          DEFAULT: '#EF4444',
          50: '#FDECEC',
          100: '#FBDADA',
          200: '#F7B5B5',
          300: '#F39090',
          400: '#F06A6A',
          500: '#EF4444',
          600: '#E51414',
          700: '#B21010',
          800: '#7A0B0B',
          900: '#420606',
        },
        dark: {
          DEFAULT: '#0F172A',
          50: '#F8FAFC',
          100: '#F1F5F9',
          200: '#E2E8F0',
          300: '#CBD5E1',
          400: '#94A3B8',
          500: '#64748B',
          600: '#475569',
          700: '#334155',
          800: '#1E293B',
          900: '#0F172A',
          950: '#020617',
        },
      },
      borderRadius: {
        'sm': '0.25rem',
        'md': '0.375rem',
        'lg': '0.5rem',
        'xl': '0.75rem',
        '2xl': '1rem',
      },
      boxShadow: {
        'sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        'DEFAULT': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        'inner': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
        'none': 'none',
        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.15)',
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      },
      animation: {
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
    },
  },
  plugins: [],
};
"""
Celery Beat Schedule Configuration
Defines periodic tasks for retry and DLQ processing
"""

from celery.schedules import crontab
from datetime import timedelta

__all__ = ['beat_schedule', 'timezone']

# Beat schedule for periodic tasks
beat_schedule = {
    # Run retry scheduler every 30 seconds
    'periodic-retry-scheduler': {
        'task': 'app.core.services.celery_conf.retry_tasks.periodic_retry_scheduler',
        'schedule': timedelta(seconds=30),  # Every 30 seconds
    },
    
    # Process DLQ entries every 1 minutes
    'process-dlq-entries': {
        'task': 'app.core.services.celery_conf.retry_tasks.process_dlq_entries',
        'schedule': crontab(minute='*/1'),  # Every 1 minutes
    },
}

# Timezone for scheduled tasks
timezone = 'UTC'

import logging
import boto3
from collections import defaultdict
from typing import Dict, List
from app.core.services.api_cloud_providers.aws.aws_service_factory import AWSServiceFactory

# Get logger instance
logger = logging.getLogger(__name__)

class ResourceExplorerService:
    """Service to discover AWS regions containing resources for enabled services."""
    
    # Service to resource type mapping - using format that works with your setup
    SERVICES_RESOURCE_TYPES = {
        'ec2': ['AWS::EC2::Instance', 'AWS::EC2::Volume', 'AWS::EC2::SecurityGroup', 'AWS::EC2::VPC'],
        'rds': ['AWS::RDS::DBInstance', 'AWS::RDS::DBCluster', 'AWS::RDS::DBSnapshot'],
        'lambda': ['AWS::Lambda::Function'],
        'ecs': ['AWS::ECS::Cluster', 'AWS::ECS::Service', 'AWS::ECS::TaskDefinition'],
        'eks': ['AWS::EKS::Cluster'],
        'elb': ['AWS::ElasticLoadBalancing::LoadBalancer', 'AWS::ElasticLoadBalancingV2::LoadBalancer'],
        'efs': ['AWS::EFS::FileSystem'],
        'elasticache': ['AWS::ElastiCache::CacheCluster', 'AWS::ElastiCache::ReplicationGroup'],
        's3': ['AWS::S3::Bucket'],
        'iam': ['AWS::IAM::Role', 'AWS::IAM::User', 'AWS::IAM::Policy']
    }
    
    def __init__(self, credentials: dict, region: str = 'ap-south-1'):
        """
        Lightweight initializer; no session initialization here.
        """
        self.credentials = credentials
        self.region = region
    
    @staticmethod
    def _extract_region_from_arn(arn: str) -> str:
        try:
            parts = arn.split(':')
            return parts[3] if len(parts) >= 4 and parts[3] else 'global'
        except Exception:
            return 'unknown'
    
    async def discover_regions_for_services(self, enabled_services: List[Dict]) -> Dict[str, List[str]]:
        """
        Discover regions containing resources for enabled services.
        Uses the working pattern from your successful code.
        """
        service_region_mapping = defaultdict(set)
        logger.info(f"🔍 Starting region discovery for {len(enabled_services)} enabled services")
        
        # Create resource-explorer-2 client specifically in ap-south-1 (sync boto3)
        # Always use default profile; do not initialize session with credentials
        # boto_session = boto3.Session()

        boto_session = boto3.Session(
            aws_access_key_id=self.credentials.get('access_key'),
            aws_secret_access_key=self.credentials.get('secret_key'),
            aws_session_token=self.credentials.get('session_token'),
            region_name='ap-south-1',
        )
        client = boto_session.client('resource-explorer-2', region_name='ap-south-1')
        
        logger.info("🔍 Searching using ap-south-1 Resource Explorer (working format)...")
        
        for service in enabled_services:
            service_name = service['name']
            if service_name not in self.SERVICES_RESOURCE_TYPES:
                logger.warning(f"⚠️ Service '{service_name}' not supported for region discovery")
                continue
                
            logger.info(f"🔍 Searching {service_name} resources...")
            resource_types = self.SERVICES_RESOURCE_TYPES[service_name]
            
            for resource_type in resource_types:
                try:
                    # Use the format that works: just the resource type without 'resourcetype:'
                    response = client.search(QueryString=resource_type, MaxResults=100)
                    resources = response.get('Resources', [])
                    
                    # Extract regions from found resources
                    for resource in resources:
                        region = resource.get('Region')
                        if region: 
                            service_region_mapping[service_name].add(region)
                    
                    logger.info(f"  ✅ {resource_type}: {len(resources)} resources")
                    
                except Exception as e:
                    logger.error(f"  ❌ {resource_type}: Error - {e}")
            
            # Also try service-based search as alternative (from your working code)
            try:
                response = client.search(QueryString=f'service:{service_name}', MaxResults=100)
                resources = response.get('Resources', [])
                
                for resource in resources:
                    region = resource.get('Region')
                    if region:
                        service_region_mapping[service_name].add(region)
                
                logger.info(f"  ✅ service:{service_name}: {len(resources)} resources in {list(service_region_mapping[service_name])}")
                
            except Exception as e:
                logger.warning(f"  ⚠️ service:{service_name}: Alternative search failed - {e}")
        
        # Convert to final format
        final_mapping = {service: list(regions) for service, regions in service_region_mapping.items()}
        
        logger.info("📊 Final Service-Region Mapping:")
        for service, regions in final_mapping.items():
            if regions:
                logger.info(f"  '{service}': {regions}")
            else:
                logger.warning(f"  '{service}': No regions found")
        
        return final_mapping
    
    async def get_regions_for_scan(self, enabled_services: List[Dict]) -> List[str]:
        """
        Get all unique regions that contain resources for any enabled service.
        """
        service_region_mapping = await self.discover_regions_for_services(enabled_services)
        
        # Collect all unique regions
        all_regions = set()
        for regions in service_region_mapping.values():
            all_regions.update(regions)
        
        unique_regions = sorted(list(all_regions))
        logger.info(f"🌍 Total unique regions for scan: {unique_regions}")
        
        return unique_regions
    
    async def get_service_region_mapping(self, enabled_services: List[Dict]) -> Dict[str, List[str]]:
        """
        Get detailed mapping of services to their regions.
        """
        return await self.discover_regions_for_services(enabled_services)
